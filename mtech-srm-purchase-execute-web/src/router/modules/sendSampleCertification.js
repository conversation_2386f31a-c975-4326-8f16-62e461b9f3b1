/*
 * @Author: wb_qianxiaoxia <EMAIL>
 * @Date: 2025-06-17 21:55:50
 * @LastEditors: wb_qianxiaoxia <EMAIL>
 * @LastEditTime: 2025-06-18 15:05:37
 * @FilePath: /mtech-srm-purchase-execute-web/src/router/modules/sendSampleCertification.js
 * @Description:
 */
const Router = [
  // 采方-送样申请单
  {
    path: 'send-sample-management',
    name: 'send-sample-management',
    component: () =>
      import('@/views/sendSampleCertificationManagement/sendSampleApplicationForm/index.vue')
  },
  // 采方-送样申请单-详情
  {
    path: 'send-sample-management-detail',
    name: 'send-sample-management-detail',
    component: () =>
      import('@/views/sendSampleCertificationManagement/sendSampleApplicationForm/detail.vue')
  },
  // 供方-送样申请单
  {
    path: 'send-sample-management-sup',
    name: 'send-sample-management-sup',
    component: () =>
      import('@/views/sendSampleCertificationManagement/sendSampleApplicationFormSup/index.vue')
  },
  // 供方-送样申请单-详情
  {
    path: 'send-sample-management-detail-sup',
    name: 'send-sample-management-detail-sup',
    component: () =>
      import('@/views/sendSampleCertificationManagement/sendSampleApplicationFormSup/detail.vue')
  },
  // 采方-认证申请单
  {
    path: 'certification-management',
    name: 'certification-management',
    component: () =>
      import('@/views/sendSampleCertificationManagement/certificationApplicationForm/index.vue')
  },
  // 采方-认证申请单-详情
  {
    path: 'certification-management-detail',
    name: 'certification-management-detail',
    component: () =>
      import('@/views/sendSampleCertificationManagement/certificationApplicationForm/detail.vue')
  },
  // 供方-认证申请单
  {
    path: 'certification-management-sup',
    name: 'certification-management-sup',
    component: () =>
      import('@/views/sendSampleCertificationManagement/certificationApplicationFormSup/index.vue')
  },
  // 供方-认证申请单-详情
  {
    path: 'certification-management-detail-sup',
    name: 'certification-management-detail-sup',
    component: () =>
      import('@/views/sendSampleCertificationManagement/certificationApplicationFormSup/detail.vue')
  }
]

export default Router
