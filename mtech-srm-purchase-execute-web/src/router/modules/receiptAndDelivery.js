const Router = [
  /**
   * 收发货协同
   *
   * 目录
   *
   *********** 采方 ***********
   * 供货计划-采方 类型：菜单
   * 送货单-列表-采方 类型：菜单
   * 送货单-详情-采方
   * 收退货记录-采方 类型：菜单
   * 预约送货-采方 类型：菜单
   * 收货-采方 类型：菜单
   * 收货-采方 类型：详情
   *
   *********** 供方 ***********
   * 供货计划-供方 类型：菜单
   * 批量创建送货单-供方
   * 送货单-列表-供方 类型：菜单
   * 手工新建无PO送货单-供方
   * 送货单-详情-供方/送货单（无采购订单）-详情-供方
   * 送货单-维护物流信息-供方
   * 送货司机信息维护-供方 类型：菜单
   * 收退货记录-供方 类型：菜单
   * 预约送货-供方 类型：菜单
   *
   *********** 加工方 ***********
   * 送货单-列表-加工方 类型：菜单
   * 送货单-详情-加工方
   *
   * ***********条码打印***********
   * 条码设置 类型：菜单
   * 条码需求管理 类型：菜单
   * 条码关联 类型：菜单
   * 条码设置
   * 出入库记录
   */
  {
    path: 'supply-plan', // 供货计划-采方 类型：菜单
    name: 'supply-plan',
    component: () => import('@/views/receiptAndDelivery/supplyPlan/index.vue')
  },
  {
    path: 'deliver-list', // 送货单-列表-采方 类型：菜单
    name: 'deliver-list',
    component: () => import('@/views/receiptAndDelivery/deliverList/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'deliver-list-tv', // 送货单-列表-采方 - 泛智屏 类型：菜单
    name: 'deliver-list-tv',
    component: () => import('@/views/receiptAndDelivery/deliverListTV/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'deliver-history-list', // 送货单-历史列表-采方 - 泛智屏 类型：菜单
    name: 'deliver-history-list',
    component: () => import('@/views/receiptAndDelivery/deliverHistoryList/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'new-mode-deliver-list-tv', // 送货单-列表- 采方 - 新模式 - 泛智屏 类型：菜单
    name: 'new-mode-deliver-list-tv',
    component: () => import('@/views/receiptAndDelivery/newModeDeliverListTV/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'deliver-detail', // 送货单-详情-采方
    name: 'deliver-detail',
    component: () => import('@/views/receiptAndDelivery/deliverDetail/index.vue')
  },
  {
    path: 'deliver-detail-tv', // 送货单-详情-采方 - 泛智屏
    name: 'deliver-detail-tv',
    component: () => import('@/views/receiptAndDelivery/deliverDetailTV/index.vue')
  },
  {
    path: 'deliver-history-detail', // 送货单历史-详情-采方 - 泛智屏
    name: 'deliver-history-detail',
    component: () => import('@/views/receiptAndDelivery/deliverHistoryDetail/index.vue')
  },
  {
    path: 'new-mode-deliver-detail-tv', // 送货单-详情-采方 - 泛智屏
    name: 'new-mode-deliver-detail-tv',
    component: () => import('@/views/receiptAndDelivery/newModeDeliverDetailTV/index.vue')
  },
  {
    path: 'receipt-return', // 收退货记录-采方 类型：菜单
    name: 'receipt-return',
    component: () => import('@/views/receiptAndDelivery/receiptReturn/index.vue')
  },
  {
    path: 'reservation-deliver', // 预约送货-采方 类型：菜单
    name: 'reservation-deliver',
    component: () => import('@/views/receiptAndDelivery/reservationDeliver/index.vue')
  },
  {
    path: 'receipt-list-processing', // 收货-采方 类型：菜单 receipt-list-processing
    name: 'receipt-list-processing',
    component: () => import('@/views/receiptAndDelivery/receiptListProcessing/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'receipt-list-detail', // 收货-采方 类型：详情
    name: 'receipt-list-detail',
    component: () => import('@/views/receiptAndDelivery/receiptListDetail/index.vue')
  },
  {
    path: 'supply-plan-supplier', // 供货计划-供方 类型：菜单
    name: 'supply-plan-supplier',
    component: () => import('@/views/receiptAndDelivery/supplyPlanSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'supply-plan-supplier-kt', // 供货计划-供方 类型：菜单
    name: 'supply-plan-supplier-kt',
    component: () => import('@/views/receiptAndDelivery/supplyPlanSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'supply-plan-supplier-tv', // 供货计划-供方-泛智屏 类型：菜单
    name: 'supply-plan-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/supplyPlanSupplierTV/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'supply-plan-procure-tv', // 供货计划-采方-泛智屏 类型：菜单
    name: 'supply-plan-procure-tv',
    component: () => import('@/views/receiptAndDelivery/supplyPlanProcureTV/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'batch-delivery-supplier', // 批量创建送货单-供方
    name: 'batch-delivery-supplier',
    component: () => import('@/views/receiptAndDelivery/batchDeliverySupplier/index.vue')
  },
  {
    path: 'batch-delivery-supplier-kt', // 批量创建送货单-供方
    name: 'batch-delivery-supplier-kt',
    component: () => import('@/views/receiptAndDelivery/batchDeliverySupplier/index.vue')
  },
  {
    path: 'batch-delivery-supplier-tv', // 批量创建送货单-供方 - 泛智屏
    name: 'batch-delivery-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/batchDeliverySupplierTV/index.vue')
    // meta: {
    //   keepAlive: true
    // }
  },
  {
    path: 'batch-delivery-supplier-screen-tv', // 屏发货指导批量创建送货单-供方 - 泛智屏
    name: 'batch-delivery-supplier-screen-tv',
    component: () => import('@/views/receiptAndDelivery/batchDeliverySupplierTV/indexForScreen.vue')
    // meta: {
    //   keepAlive: true
    // }
  },
  {
    path: 'deliver-list-supplier', // 送货单-列表-供方 类型：菜单
    name: 'deliver-list-supplier',
    component: () => import('@/views/receiptAndDelivery/deliverListSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'deliver-list-supplier-kt', // 送货单-列表-供方 类型：菜单
    name: 'deliver-list-supplier-kt',
    component: () => import('@/views/receiptAndDelivery/deliverListSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'deliver-list-supplier-tv', // 送货单-列表-供方 - 泛智屏 类型：菜单
    name: 'deliver-list-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/deliverListSupplierTV/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'deliver-history-list-supplier', // 送货单-历史数据列表-供方 类型：菜单
    name: 'deliver-history-list-supplier',
    component: () => import('@/views/receiptAndDelivery/deliverHistoryListSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'deliver-history-list-supplier-kt', // 送货单-历史数据列表-供方 类型：菜单
    name: 'deliver-history-list-supplier-kt',
    component: () => import('@/views/receiptAndDelivery/deliverHistoryListSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'deliver-history-list-supplier-tv', // 送货单-历史数据列表-供方 类型：菜单
    name: 'deliver-history-list-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/deliverHistoryListSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'new-mode-deliver-list-supplier-tv', // 新模式 - 送货单-列表- 供方 - 泛智屏 类型：菜单
    name: 'new-mode-deliver-list-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/newModeDeliverListSupplierTV/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'new-mode-deliver-detail-supplier-tv', // 送货单-详情-供方/送货单（无采购订单）-详情-供方
    name: 'new-mode-deliver-detail-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/newModeDeliverDetailSupplierTV/index.vue')
  },
  {
    path: 'create-noOrder-supplier', // 手工新建无PO送货单-供方
    name: 'create-noOrder-supplier',
    component: () => import('@/views/receiptAndDelivery/createNoOrderSupplier/index.vue')
  },
  {
    path: 'create-noOrder-supplier-tv', // 手工新建无PO送货单-供方
    name: 'create-noOrder-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/createNoOrderSupplierTV/index.vue')
  },
  {
    path: 'deliver-detail-supplier', // 送货单-详情-供方/送货单（无采购订单）-详情-供方
    name: 'deliver-detail-supplier',
    component: () => import('@/views/receiptAndDelivery/deliverDetailSupplier/index.vue')
  },
  {
    path: 'deliver-detail-supplier-tv', // 送货单-详情-供方/送货单（无采购订单）-详情-供方
    name: 'deliver-detail-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/deliverDetailSupplierTV/index.vue')
  },
  {
    path: 'deliver-history-detail-supplier', // 送货单历史-详情-供方/送货单（无采购订单）-详情-供方
    name: 'deliver-history-detail-supplier',
    component: () => import('@/views/receiptAndDelivery/deliverHistoryDetailSupplier/index.vue')
  },
  {
    path: 'deliver-logistics-supplier', // 送货单-维护物流信息-供方
    name: 'deliver-logistics-supplier',
    component: () => import('@/views/receiptAndDelivery/deliverLogisticsSupplier/index.vue')
  },
  {
    path: 'driver-info-supplier-tv', // 送货司机信息维护-供方 类型：菜单
    name: 'driver-info-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/driverInfoSupplier/index.vue')
  },
  {
    path: 'driver-info-supplier-kt', // 送货司机信息维护-供方 类型：菜单
    name: 'driver-info-supplier-kt',
    component: () => import('@/views/receiptAndDelivery/driverInfoSupplier/index.vue')
  },
  {
    path: 'driver-info-supplier', // 送货司机信息维护-供方 类型：菜单
    name: 'driver-info-supplier',
    component: () => import('@/views/receiptAndDelivery/driverInfoSupplier/index.vue')
  },
  {
    path: 'receipt-return-supplier-tv', // 收退货记录-供方 类型：菜单
    name: 'receipt-return-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/receiptReturnSupplier/index.vue')
  },
  {
    path: 'receipt-return-supplier-kt', // 收退货记录-供方 类型：菜单
    name: 'receipt-return-supplier-kt',
    component: () => import('@/views/receiptAndDelivery/receiptReturnSupplier/index.vue')
  },
  {
    path: 'receipt-return-supplier', // 收退货记录-供方 类型：菜单
    name: 'receipt-return-supplier',
    component: () => import('@/views/receiptAndDelivery/receiptReturnSupplier/index.vue')
  },
  {
    path: 'reservation-deliver-supplier', // 预约送货-供方 类型：菜单
    name: 'reservation-deliver-supplier',
    component: () => import('@/views/receiptAndDelivery/reservationDeliverSupplier/index.vue')
  },
  {
    path: 'reservation-deliver-supplier-kt', // 预约送货-供方 类型：菜单
    name: 'reservation-deliver-supplier-kt',
    component: () => import('@/views/receiptAndDelivery/reservationDeliverSupplier/index.vue')
  },
  {
    path: 'reservation-deliver-supplier-tv', // 预约送货-供方-泛智屏 类型：菜单
    name: 'reservation-deliver-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/reservationDeliverSupplierTV/index.vue')
  },
  {
    path: 'deliver-list-processing-tv', // 送货单-列表-加工方 类型：菜单
    name: 'deliver-list-processing-tv',
    component: () => import('@/views/receiptAndDelivery/deliverListProcessing/index.vue')
  },
  {
    path: 'deliver-list-processing-kt', // 送货单-列表-加工方 类型：菜单
    name: 'deliver-list-processing-kt',
    component: () => import('@/views/receiptAndDelivery/deliverListProcessing/index.vue')
  },
  {
    path: 'deliver-list-processing', // 送货单-列表-加工方 类型：菜单
    name: 'deliver-list-processing',
    component: () => import('@/views/receiptAndDelivery/deliverListProcessing/index.vue')
  },
  {
    path: 'deliver-detail-processing', // 送货单-详情-加工方
    name: 'deliver-detail-processing',
    component: () => import('@/views/receiptAndDelivery/deliverDetailProcessing/index.vue')
  },
  {
    path: 'barcode-require-management', // 条码需求管理
    name: 'barcode-require-management',
    component: () =>
      import('@/views/receiptAndDelivery/barcodePrinting/requireManagement/index.vue')
  },
  {
    path: 'barcode-require-management-kt', // 条码需求管理 空调
    name: 'barcode-require-management-kt',
    component: () =>
      import('@/views/receiptAndDelivery/barcodePrinting/requireManagementKt/index.vue')
  },
  {
    path: 'barcode-require-management-tv', // 条码需求管理 泛智屏
    name: 'barcode-require-management-tv',
    component: () =>
      import('@/views/receiptAndDelivery/barcodePrinting/requireManagementTV/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'barcode-require-setup-tv', // 条码需求管理配置页
    name: 'barcode-require-setup-tv',
    component: () =>
      import('@/views/receiptAndDelivery/barcodePrinting/barcodeRequireSetup/index.vue')
  },
  {
    path: 'barcode-require-setup-kt', // 条码需求管理配置页
    name: 'barcode-require-setup-kt',
    component: () =>
      import('@/views/receiptAndDelivery/barcodePrinting/barcodeRequireSetup/index.vue')
  },
  {
    path: 'barcode-require-setup', // 条码需求管理配置页
    name: 'barcode-require-setup',
    component: () =>
      import('@/views/receiptAndDelivery/barcodePrinting/barcodeRequireSetup/index.vue')
  },
  {
    path: 'barcode-association', // 条码关联
    name: 'barcode-association',
    component: () =>
      import('@/views/receiptAndDelivery/barcodePrinting/barcodeAssociation/index.vue')
  },
  {
    path: 'barcode-association-kt', // 条码关联 kt
    name: 'barcode-association-kt',
    component: () =>
      import('@/views/receiptAndDelivery/barcodePrinting/barcodeAssociationKt/index.vue')
  },
  {
    path: 'barcode-association-tv', // 条码关联 tv
    name: 'barcode-association-tv',
    component: () =>
      import('@/views/receiptAndDelivery/barcodePrinting/barcodeAssociationTV/index.vue')
  },
  {
    path: 'barcode-setup', // 条码打印-条码设置
    name: 'barcode-setup',
    component: () => import('@/views/receiptAndDelivery/barcodePrinting/barcodeSetup/index.vue')
  },
  {
    path: 'barcode-setup-kt', // 条码打印-条码设置 kt
    name: 'barcode-setup-kt',
    component: () => import('@/views/receiptAndDelivery/barcodePrinting/barcodeSetupKt/index.vue')
  },
  {
    path: 'barcode-setup-tv', // 条码打印-条码设置 tv
    name: 'barcode-setup-tv',
    component: () => import('@/views/receiptAndDelivery/barcodePrinting/barcodeSetupTV/index.vue')
  },
  {
    path: 'inout-record', // 出入库记录
    name: 'inout-record',
    component: () => import('@/views/receiptAndDelivery/inoutRecord/index.vue')
  },
  {
    path: 'inout-record-supplier-tv', // 出入库记录供方
    name: 'inout-record-supplier-tv',
    component: () => import('@/views/receiptAndDelivery/inoutRecordSupplier/index.vue')
  },
  {
    path: 'inout-record-supplier-kt', // 出入库记录供方
    name: 'inout-record-supplier-kt',
    component: () => import('@/views/receiptAndDelivery/inoutRecordSupplier/index.vue')
  },
  {
    path: 'inout-record-supplier-gf', // 出入库记录供方
    name: 'inout-record-supplier-gf',
    component: () => import('@/views/receiptAndDelivery/inoutRecordSupplier/index.vue')
  },
  {
    path: 'inout-record-supplier', // 出入库记录供方
    name: 'inout-record-supplier',
    component: () => import('@/views/receiptAndDelivery/inoutRecordSupplier/index.vue')
  },
  {
    path: 'storage-not-priced', // 入库未定价
    name: 'storageNotPriced',
    component: () => import('@/views/receiptAndDelivery/storageNotPriced/index.vue')
  },
  {
    path: 'nopo-delivery-request-supplier',
    name: 'nopo-delivery-request-supplier',
    component: () => import('@/views/receiptAndDelivery/nopoDeliveryRequest/index.vue')
  },
  {
    path: 'nopo-delivery-request-purchase',
    name: 'nopo-delivery-request-purchase',
    component: () => import('@/views/receiptAndDelivery/nopoDeliveryRequestPur/index.vue')
  },
  {
    path: 'create-nopo-delivery-request-supplier', // 手工新建无PO送货单-供方
    name: 'create-nopo-delivery-request-supplier',
    component: () =>
      import('@/views/receiptAndDelivery/nopoDeliveryRequest/components/addAndEdit.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'create-nopo-delivery-request-purchase', // 手工新建无PO送货单-采方
    name: 'create-nopo-delivery-request-purchase',
    component: () =>
      import('@/views/receiptAndDelivery/nopoDeliveryRequestPur/components/addAndEdit.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'detail-nopo-delivery-request-supplier', // 无PO送货单明细-供方
    name: 'detail-nopo-delivery-request-supplier',
    component: () => import('@/views/receiptAndDelivery/nopoDeliveryRequest/components/detail.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'detail-nopo-delivery-request-purchase', // 无PO送货单明细-采方
    name: 'detail-nopo-delivery-request-purchase',
    component: () =>
      import('@/views/receiptAndDelivery/nopoDeliveryRequestPur/components/detail.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'supplier-order-delivery', // 送货单维护订单 - 供方
    name: 'supplier-order-delivery',
    component: () => import('@/views/receiptAndDelivery/supplierOrderDeliveryTV/index.vue')
  },
  {
    path: 'purchase-order-delivery', // 送货单维护订单 - 采方
    name: 'purchase-order-delivery',
    component: () => import('@/views/receiptAndDelivery/purChaseOrderDeliveryTV/index.vue')
  },
  {
    path: 'sap-inventory', // SAP库存查询 - 供方
    name: 'sap-inventory',
    component: () => import('@/views/receiptAndDelivery/sapInventory/index.vue')
  },
  {
    path: 'customs-declaration-elements', // 报关要素-采方
    name: 'customs-declaration-elements',
    component: () => import('@/views/receiptAndDelivery/customsDeclarationElements/index.vue')
  },
  {
    path: 'customs-declaration-elements-supplier', // 报关要素-供方
    name: 'customs-declaration-elements-supplier',
    component: () =>
      import('@/views/receiptAndDelivery/customsDeclarationElementsSupplier/index.vue')
  },
  {
    path: 'customs-declaration-elements-new', // 报关要素-采方-新
    name: 'customs-declaration-elements-new',
    component: () => import('@/views/receiptAndDelivery/customsDeclarationElementsNew/index.vue')
  },
  {
    path: 'customs-declaration-elements-sup-new', // 报关要素-供方-新
    name: 'customs-declaration-elements-sup-new',
    component: () => import('@/views/receiptAndDelivery/customsDeclarationElementsSupNew/index.vue')
  },
  {
    path: 'report/overseas-receipts-diff', // 海外收货差异报表
    name: 'report/overseas-receipts-diff',
    component: () => import('@/views/report/overseasReceiptsDiff/index.vue')
  },
  {
    path: 'supplier-system-delivery', // 采方-收货协同-供方系统送货单
    name: 'supplier-system-delivery',
    component: () => import('@/views/receiptAndDelivery/supplierSystemDelivery/index.vue')
  },
  {
    path: 'system-delivery', // 供方-送货管理-本方系统送货单
    name: 'system-delivery',
    component: () => import('@/views/receiptAndDelivery/systemDelivery/index.vue')
  },
  {
    path: 'yx-receipt-report', // 蕴鑫收货报表-采方
    name: 'yx-receipt-report',
    component: () => import('@/views/receiptAndDelivery/yxReceiptReport/index.vue')
  },
  {
    path: 'yx-receipt-report-sup', // 蕴鑫收货报表-供方
    name: 'yx-receipt-report-sup',
    component: () => import('@/views/receiptAndDelivery/yxReceiptReportSup/index.vue')
  },
  {
    path: 'scan-loading-record', // 扫描装车记录-采方
    name: 'scan-loading-record',
    component: () => import('@/views/receiptAndDelivery/scanLoadingRecord/index.vue')
  },
  {
    path: 'scan-loading-record-sup', // 扫描装车记录-供方
    name: 'scan-loading-record-sup',
    component: () => import('@/views/receiptAndDelivery/scanLoadingRecordSup/index.vue')
  }
]

export default Router
