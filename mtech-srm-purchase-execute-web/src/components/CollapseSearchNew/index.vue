<template>
  <div class="collapse-search-container toggle-container" :class="{ isExpended: isExpended }">
    <!-- 操作按钮栏 -->
    <div class="action-bar">
      <div class="left-actions">
        <div class="toggle-tag" @click="isExpended = !isExpended">
          <span>{{ isExpended ? $t('收起') : $t('展开') }}</span>
          <i
            class="mt-icons mt-icon-MT_DownArrow"
            :class="isExpended ? 'expendIcon' : 'unExpendIcon'"
          />
        </div>
        <!-- 搜索模板下拉 -->
        <div v-if="enableSearchTemplate && gridId && isExpended" class="template-dropdown">
          <div class="custom-select-wrapper">
            <div
              class="custom-select-trigger"
              @click="toggleTemplateDropdown"
              :class="{ 'is-open': showTemplateDropdown }"
            >
              <span class="selected-text">
                {{ currentSearchTemplate || $t('选择搜索模板') }}
              </span>
              <i
                class="mt-icons mt-icon-MT_DownArrow"
                :class="{ rotated: showTemplateDropdown }"
              ></i>
            </div>
            <div v-if="showTemplateDropdown" class="custom-select-dropdown">
              <div
                v-for="item in allTemplateOptions"
                :key="item.templateName"
                class="template-option"
                :class="{
                  'is-default': item.isDefault,
                  'is-selected': currentSearchTemplate === item.templateName
                }"
                @click="selectTemplate(item)"
              >
                <span class="template-name">{{ item.templateName }}</span>
                <div class="template-actions" @click.stop>
                  <span v-if="item.isDefault" class="default-tag">{{ $t('默认') }}</span>
                  <div class="action-buttons">
                    <!-- 设为默认按钮：所有模板都可以设为默认 -->
                    <i
                      class="action-btn mt-icons vxe-icon-star-fill"
                      :class="{ active: item.isDefault }"
                      @click.stop="toggleDefaultTemplate(item)"
                      :title="item.isDefault ? $t('取消默认') : $t('设为默认')"
                    ></i>
                    <!-- 重命名和删除按钮：只有用户自定义模板才显示 -->
                    <template v-if="!item.isSystemDefault">
                      <i
                        class="action-btn mt-icons vxe-icon-edit"
                        @click.stop="renameTemplate(item)"
                        :title="$t('重命名')"
                      ></i>
                      <i
                        class="action-btn mt-icons vxe-icon-delete"
                        @click.stop="removeSearchTemplate(item)"
                        :title="$t('删除')"
                      ></i>
                    </template>
                  </div>
                </div>
              </div>
              <div v-if="allTemplateOptions.length === 0" class="no-options">
                {{ $t('暂无模板') }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="right-actions">
        <span
          v-if="enableSearchConditionSetting"
          class="action-btn"
          :class="{ 'btn-hidden': !isExpended }"
          type="info"
          @click="openConditionSetting"
          >{{ $t('设置搜索条件') }}</span
        >
        <span
          v-if="enableSearchTemplate && gridId"
          class="action-btn"
          :class="{ 'btn-hidden': !isExpended }"
          type="info"
          @click="saveAsTemplate()"
          >{{ $t('保存为模板') }}</span
        >
        <span class="action-btn" type="info" @click="reset()">{{ $t('重置') }}</span>
        <span class="action-btn" type="primary" @click="search()">{{ $t('查询') }}</span>
      </div>
    </div>

    <!-- 搜索条件区域 -->
    <div
      class="search-area"
      :class="{ opend: isExpended }"
      :style="getCustomStyle()"
      @keyup.enter="handleEnterKeyup"
    >
      <slot :visible-conditions="currentVisibleConditions" :is-expanded="isExpended"></slot>
    </div>

    <!-- 搜索条件设置对话框 -->
    <condition-setting-dialog
      v-if="showConditionDialog"
      :dialog-data="conditionDialogData"
      @handleDialogShow="handleConditionDialogClose"
      @confirmSuccess="handleConditionSettingSuccess"
    />

    <!-- 保存搜索模板对话框 -->
    <save-template-dialog
      v-if="showSaveDialog"
      :dialog-data="saveDialogData"
      @handleAddDialogShow="handleSaveDialogClose"
      @confirmSuccess="handleSaveSuccess"
    />

    <!-- 重命名搜索模板对话框 -->
    <rename-template-dialog
      v-if="showRenameDialog"
      :dialog-data="renameDialogData"
      @handleRenameDialogShow="handleRenameDialogClose"
      @confirmSuccess="handleRenameSuccess"
    />
  </div>
</template>

<script>
import SaveTemplateDialog from './SaveTemplateDialog.vue'
import RenameTemplateDialog from './RenameTemplateDialog.vue'
import ConditionSettingDialog from './ConditionSettingDialog.vue'
import { API } from '@mtech-common/http'

const saveUserMemory = '/lowcodeWeb/tenant/user-memory/save'
const getUserMemory = '/lowcodeWeb/tenant/user-memory/get'

export default {
  components: {
    SaveTemplateDialog,
    RenameTemplateDialog,
    ConditionSettingDialog
  },
  props: {
    backgroundColor: {
      type: String,
      require: false,
      default: '#f9f9f9'
    },
    showButton: {
      type: Boolean,
      require: false,
      default: true
    },
    defaultMaxHeight: {
      type: [String, Number],
      default: '70'
    },
    maxHeight: {
      type: String,
      default: '600px'
    },
    minRows: {
      type: [String, Number],
      default: 1
    },
    // 是否为gird布局
    isGridDisplay: {
      type: Boolean,
      default: false
    },
    expended: {
      type: Boolean,
      default: false
    },
    // 是否启用搜索模板功能
    enableSearchTemplate: {
      type: Boolean,
      default: false
    },
    // 网格ID，用于存储搜索模板
    gridId: {
      type: String,
      default: ''
    },
    // 默认搜索模板（开发者预设）
    defaultSearchTemplates: {
      type: Array,
      default: () => []
    },
    // 当前搜索表单数据
    searchFormData: {
      type: Object,
      default: () => ({})
    },
    // 是否启用搜索条件设置功能
    enableSearchConditionSetting: {
      type: Boolean,
      default: false
    },
    // 可用的搜索条件配置
    availableConditions: {
      type: Array,
      default: () => []
    },
    // 不可隐藏的搜索条件字段
    requiredConditions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isExpended: false,
      // 搜索模板相关数据
      searchTemplates: [], // 用户自定义搜索模板
      currentSearchTemplate: '', // 当前选中的搜索模板
      defaultTemplate: '', // 默认搜索模板
      showSaveDialog: false, // 是否显示保存模板对话框
      saveDialogData: {}, // 保存对话框数据
      showRenameDialog: false, // 是否显示重命名模板对话框
      renameDialogData: {}, // 重命名对话框数据
      isLoadingTemplates: false, // 是否正在加载模板
      showTemplateDropdown: false, // 是否显示模板下拉框
      // 搜索条件设置相关
      showConditionDialog: false, // 是否显示条件设置对话框
      conditionDialogData: {}, // 条件设置对话框数据
      visibleConditions: [], // 当前显示的搜索条件
      conditionOrder: [], // 搜索条件显示顺序
      userSelectedConditions: [] // 用户自定义选择的搜索条件
    }
  },
  computed: {
    // 所有模板选项（用于下拉选择）
    allTemplateOptions() {
      const options = []

      // 添加默认模板（系统预设，不可删除）
      this.defaultSearchTemplates.forEach((template) => {
        options.push({
          ...template,
          isDefault: this.defaultTemplate === template.templateName,
          isSystemDefault: true
        })
      })

      // 添加用户自定义模板
      this.searchTemplates.forEach((template) => {
        options.push({
          ...template,
          isDefault: this.defaultTemplate === template.templateName,
          isSystemDefault: false
        })
      })

      return options
    },
    // 当前显示的搜索条件
    currentVisibleConditions() {
      if (!this.availableConditions || this.availableConditions.length === 0) {
        return []
      }

      // 获取实际要显示的条件
      let conditionsToShow = this.getActualVisibleConditions()

      // 如果展开，显示所有条件；如果收起，只显示前5个条件
      if (this.isExpended) {
        return conditionsToShow
      } else {
        return conditionsToShow.slice(0, 5)
      }
    }
  },
  watch: {
    expended: {
      handler: function (val) {
        this.isExpended = val
      },
      immediate: true
    },
    gridId: {
      handler: function (val) {
        if (val && this.enableSearchTemplate) {
          this.searchTemplateInit()
        }
      },
      immediate: true
    },
    availableConditions: {
      handler: function (val) {
        if (val && val.length > 0) {
          // 初始化搜索条件显示顺序
          if (this.conditionOrder.length === 0) {
            this.conditionOrder = val.map((item) => this.getFieldName(item))
          }
          // 初始化条件设置
          this.initializeConditionSettings()
        }
      },
      immediate: true
    },
    // 监听展开状态变化，更新显示的搜索条件
    isExpended: {
      handler: function () {
        // 当展开状态改变时，触发重新渲染
        this.$nextTick(() => {
          this.$emit('visible-conditions-change', this.currentVisibleConditions)
        })
      }
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside)

    // 如果没有启用搜索模板功能，直接初始化搜索条件状态
    // 如果启用了搜索模板功能，会在 gridId watcher 中的 searchTemplateInit() 完成后自动初始化
    if (!this.enableSearchTemplate || !this.gridId) {
      this.initializeConditionState()
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    // 获取字段名称（兼容 field 和 prop 两种属性）
    // 支持传入的数组字段既可以是 field 也可以是 prop
    getFieldName(item) {
      return item.field || item.prop
    },

    // 初始化搜索条件状态
    initializeConditionState() {
      if (!this.gridId) return

      try {
        const gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
        // 统一数据结构：如果有 gridMemory 属性，使用它；否则直接使用 gridInfo
        const gridMemory = gridInfo.gridMemory || gridInfo

        // 优先级1：如果有默认模板，优先使用默认模板
        if (gridMemory.defaultTemplate) {
          const defaultTemplate = this.allTemplateOptions.find(
            (t) => t.templateName === gridMemory.defaultTemplate
          )
          if (defaultTemplate) {
            console.log('初始化默认模板:', defaultTemplate.templateName)

            // 设置当前模板为默认模板
            this.currentSearchTemplate = defaultTemplate.templateName
            this.defaultTemplate = defaultTemplate.templateName

            // 恢复显示状态 - 以 searchTemplates 中的 visibleConditions 为准
            this.restoreTemplateConditionState(defaultTemplate)

            // 获取搜索数据 - 兼容多种字段名
            const searchData = defaultTemplate.searchConditions || defaultTemplate.searchRule || defaultTemplate.searchRules

            // 使用 nextTick 确保组件初始化完成后再触发事件
            this.$nextTick(() => {
              // 如果有搜索条件，先恢复到表单
              if (searchData && Object.keys(searchData).length > 0) {
                console.log('初始化默认模板，恢复搜索条件:', searchData)
                this.$emit('restoreSearchFormData', searchData)
              }

              // 无论是否有搜索条件，都触发搜索事件，让父组件决定如何处理
              setTimeout(() => {
                console.log('初始化默认模板，触发搜索:', searchData || {})
                this.$emit('templateSearch', searchData || {})
              }, 50)
            })
            return
          }
        }

        // 优先级2：如果有当前选中的模板，恢复该模板的状态
        if (gridMemory.currentSearchTemplate) {
          const currentTemplate = this.allTemplateOptions.find(
            (t) => t.templateName === gridMemory.currentSearchTemplate
          )
          if (currentTemplate) {
            // 先恢复搜索条件的值，再恢复显示状态
            const searchData = currentTemplate.searchConditions || currentTemplate.searchRule || currentTemplate.searchRules
            if (searchData && Object.keys(searchData).length > 0) {
              this.applySearchConditions(searchData)
            }

            // 然后恢复显示状态 - 以 searchTemplates 中的 visibleConditions 为准
            this.restoreTemplateConditionState(currentTemplate)
            return
          }
        }

        // 如果没有当前模板，但有保存的搜索条件状态，则恢复
        // 注意：这里的 visibleConditions 应该是非模板状态下的通用设置
        if (gridMemory.visibleConditions && gridMemory.visibleConditions.length > 0) {
          this.visibleConditions = gridMemory.visibleConditions

          // 重要：更新 userSelectedConditions
          this.userSelectedConditions = gridMemory.visibleConditions
            .map((fieldName) => {
              return this.availableConditions.find(
                (condition) => this.getFieldName(condition) === fieldName
              )
            })
            .filter(Boolean)

          this.$emit('visible-conditions-change', gridMemory.visibleConditions)
        }

        if (gridMemory.conditionOrder && gridMemory.conditionOrder.length > 0) {
          this.conditionOrder = gridMemory.conditionOrder
        }
      } catch (error) {
        console.error('初始化搜索条件状态失败:', error)
      }
    },

    // 切换模板下拉框显示状态
    toggleTemplateDropdown() {
      this.showTemplateDropdown = !this.showTemplateDropdown
    },
    // 选择模板
    selectTemplate(item) {
      console.log('=== 选择模板 ===')
      console.log('选择的模板名:', item.templateName)
      console.log('当前模板:', this.currentSearchTemplate)
      console.log('模板完整数据结构:', JSON.stringify(item, null, 2))

      // 立即更新当前选中的模板，确保UI状态正确
      this.currentSearchTemplate = item.templateName
      this.showTemplateDropdown = false

      // 强制更新视图，确保选中状态正确显示
      this.$forceUpdate()

      // 触发模板变化处理
      this.onTemplateChange(item.templateName)
    },
    // 点击外部关闭下拉框
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.showTemplateDropdown = false
      }
    },
    getCustomStyle() {
      return {
        backgroundColor: this.backgroundColor,
        maxHeight: this.isExpended
          ? this.maxHeight
          : this.minRows
          ? `${this.minRows * Number(this.defaultMaxHeight)}px`
          : `${this.defaultMaxHeight}px`
      }
    },
    reset() {
      // 只重置查询条件的值，不重置模板选择
      this.$emit('reset')
    },
    search() {
      this.$emit('search')
    },
    handleEnterKeyup(e) {
      // 文本域，回车是换行，不触发查询
      if (e.target.type === 'textarea') {
        return
      }
      this.search()
    },
    // 搜索模板相关方法
    searchTemplateInit() {
      if (!this.gridId) return

      // 首先尝试从sessionStorage获取
      let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
      // 统一数据结构：如果有 gridMemory 属性，使用它；否则直接使用 _gridInfo
      let gridMemory = _gridInfo.gridMemory || _gridInfo

      // 总是使用最新的 sessionStorage 数据，如果没有则从服务器获取
      if (gridMemory.searchTemplates) {
        // 使用sessionStorage中的数据（包括刚保存的模板）
        this.updateTemplateState(gridMemory)

        // 模板数据更新完成后，初始化搜索条件状态
        this.$nextTick(() => {
          this.initializeConditionState()
        })
      } else {
        // 如果sessionStorage中没有数据，从服务器获取
        this.isLoadingTemplates = true
        API.get(getUserMemory, {
          gridId: this.gridId
        })
          .then((res) => {
            if (res.code === 200 && res.data) {
              const serverData = res.data
              // 统一存储格式：确保存储的是 gridMemory 结构
              const normalizedData = serverData.gridMemory || serverData
              sessionStorage.setItem(this.gridId, JSON.stringify({ gridMemory: normalizedData }))
              // 更新组件状态
              this.updateTemplateState(normalizedData)
            } else {
              // 服务器没有数据，使用默认状态
              this.updateTemplateState(gridMemory)
            }

            // 模板数据更新完成后，初始化搜索条件状态
            this.$nextTick(() => {
              this.initializeConditionState()
            })
          })
          .catch((error) => {
            console.error('获取用户内存失败:', error)
            // 出错时使用sessionStorage中的数据
            this.updateTemplateState(gridMemory)

            // 即使出错也要初始化搜索条件状态
            this.$nextTick(() => {
              this.initializeConditionState()
            })
          })
          .finally(() => {
            this.isLoadingTemplates = false
          })
      }
    },

    // 更新模板状态
    updateTemplateState(gridMemory, preserveCurrentTemplate = false) {
      if (gridMemory?.searchTemplates?.length > 0) {
        this.searchTemplates = gridMemory.searchTemplates
      } else {
        this.searchTemplates = []
      }

      // 如果需要保持当前模板选择，则不更新 currentSearchTemplate
      if (!preserveCurrentTemplate) {
        if (gridMemory?.defaultTemplate) {
          this.defaultTemplate = gridMemory.defaultTemplate
          this.currentSearchTemplate = gridMemory.defaultTemplate
        } else if (gridMemory?.currentSearchTemplate) {
          this.currentSearchTemplate = gridMemory.currentSearchTemplate
        } else if (this.defaultSearchTemplates.length > 0) {
          this.currentSearchTemplate = this.defaultSearchTemplates[0].templateName
        }
      } else {
        // 只更新 defaultTemplate，保持 currentSearchTemplate 不变
        if (gridMemory?.defaultTemplate) {
          this.defaultTemplate = gridMemory.defaultTemplate
        }
      }
    },
    // 模板选择变化处理
    onTemplateChange(templateName) {
      if (!templateName) {
        this.clearCurrentTemplate()
        return
      }

      const template = this.allTemplateOptions.find((t) => t.templateName === templateName)
      if (template) {
        this.searchByTemplate(template)
      }
    },

    searchByTemplate(item) {
      console.log('=== 开始切换模板 ===')
      console.log('切换到模板:', item.templateName)
      console.log('模板完整数据:', item)

      this.currentSearchTemplate = item.templateName

      // 获取搜索数据 - 兼容多种字段名
      const searchData = item.searchConditions || item.searchRule || item.searchRules
      console.log('模板搜索数据:', searchData)
      console.log('searchConditions:', item.searchConditions)
      console.log('searchRule:', item.searchRule)
      console.log('searchRules:', item.searchRules)

      // 恢复搜索条件的显示状态和顺序
      this.restoreTemplateConditionState(item)

      // 更新内存中的当前模板（统一处理，不区分默认模板和自定义模板）
      let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
      let _gridMemory = _gridInfo.gridMemory || _gridInfo
      _gridMemory.currentSearchTemplate = item.templateName

      // 以模板中的 visibleConditions 和 conditionOrder 为准，保存到内存
      // 这确保了切换模板时严格按照模板中保存的设置来恢复状态
      if (item.visibleConditions) {
        _gridMemory.visibleConditions = item.visibleConditions
      }
      if (item.conditionOrder) {
        _gridMemory.conditionOrder = item.conditionOrder
      }

      // 统一存储格式
      sessionStorage.setItem(this.gridId, JSON.stringify({ gridMemory: _gridMemory }))

      console.log('准备触发事件...')

      // 使用 nextTick 确保 DOM 更新后再触发事件
      this.$nextTick(() => {
        console.log('nextTick 执行中...')

        // 先恢复搜索条件的值到表单中（确保回显正确）
        if (searchData && Object.keys(searchData).length > 0) {
          console.log('恢复搜索条件到表单:', searchData)
          this.$emit('restoreSearchFormData', searchData)
        } else {
          console.log('模板没有搜索条件，跳过表单恢复')
        }

        // 延迟触发搜索事件，确保表单数据已经恢复
        // 无论搜索数据是否为空，都要触发搜索事件，让父组件决定如何处理
        setTimeout(() => {
          console.log('准备触发 templateSearch 事件:', searchData || {})
          this.$emit('templateSearch', searchData || {})
          console.log('templateSearch 事件已触发')
        }, 10)
      })

      // 如果是用户自定义模板，异步保存到服务器
      if (!this.defaultSearchTemplates.find((t) => t.templateName === item.templateName)) {
        console.log('保存用户自定义模板到服务器')
        API.post(saveUserMemory, {
          gridId: this.gridId,
          gridMemory: _gridMemory
        }).catch((error) => {
          console.error('保存用户内存失败:', error)
        })
      }

      console.log('=== 模板切换处理完成 ===')
    },

    // 恢复模板的搜索条件状态
    restoreTemplateConditionState(template) {
      // 严格以 searchTemplates 中的 visibleConditions 为准
      if (template.visibleConditions && template.visibleConditions.length > 0) {
        // 兼容性处理：检查 visibleConditions 的数据格式
        let templateVisibleConditions
        if (typeof template.visibleConditions[0] === 'string') {
          // 如果是字符串数组（字段名数组），直接使用
          templateVisibleConditions = [...template.visibleConditions]
        } else if (
          typeof template.visibleConditions[0] === 'object' &&
          (template.visibleConditions[0].field || template.visibleConditions[0].prop)
        ) {
          // 如果是对象数组，提取字段名
          templateVisibleConditions = template.visibleConditions.map((condition) =>
            this.getFieldName(condition)
          )
        } else {
          // 未知格式，使用原数据
          templateVisibleConditions = [...template.visibleConditions]
        }

        // 更新可见条件 - 严格按照模板中保存的 visibleConditions
        this.visibleConditions = templateVisibleConditions

        // 重要：更新 userSelectedConditions，这样 getActualVisibleConditions 才能返回正确的条件
        this.userSelectedConditions = templateVisibleConditions
          .map((fieldName) => {
            return this.availableConditions.find(
              (condition) => this.getFieldName(condition) === fieldName
            )
          })
          .filter(Boolean) // 过滤掉 undefined 的条件

        // 处理条件顺序 - 有 visibleConditions 的情况
        if (template.conditionOrder && template.conditionOrder.length > 0) {
          this.conditionOrder = template.conditionOrder
        } else {
          // 如果没有保存顺序，使用模板的 visibleConditions 作为默认顺序
          this.conditionOrder = [...templateVisibleConditions]
        }

        // 使用 nextTick 确保在搜索条件值恢复后再通知显示状态变化
        this.$nextTick(() => {
          this.$emit('visible-conditions-change', templateVisibleConditions)
        })
      } else {
        // 如果模板中没有保存可见条件，则显示所有可用条件
        const allConditionFields = this.availableConditions.map((c) => this.getFieldName(c))
        this.visibleConditions = allConditionFields

        // 重要：更新 userSelectedConditions，显示所有可用条件
        this.userSelectedConditions = [...this.availableConditions]

        // 处理条件顺序 - 无 visibleConditions 的情况
        if (template.conditionOrder && template.conditionOrder.length > 0) {
          this.conditionOrder = template.conditionOrder
        } else {
          // 如果没有保存顺序，使用所有可用条件作为顺序
          this.conditionOrder = allConditionFields
        }

        this.$nextTick(() => {
          this.$emit('visible-conditions-change', allConditionFields)
        })
      }

      // 使用 nextTick 确保在DOM更新后触发设置变化事件
      this.$nextTick(() => {
        this.$emit('conditionSettingChange', {
          visibleConditions: this.userSelectedConditions,
          conditionOrder: this.conditionOrder,
          actualVisibleConditions: this.currentVisibleConditions
        })
      })
    },
    removeSearchTemplate(item) {
      this.$dialog({
        data: {
          title: this.$t('确认删除'),
          message: this.$t('确定要删除搜索模板') + ` "${item.templateName}" ` + this.$t('吗？')
        },
        success: () => {
          let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
          let _gridMemory = _gridInfo.gridMemory || _gridInfo
          if (_gridMemory?.searchTemplates?.length > 0) {
            _gridMemory.searchTemplates = _gridMemory.searchTemplates.filter(
              (t) => t.templateName !== item.templateName
            )
          }

          // 如果删除的是当前选中的模板，清空选中状态
          if (this.currentSearchTemplate === item.templateName) {
            this.currentSearchTemplate = ''
            _gridMemory.currentSearchTemplate = ''
          }

          // 如果删除的是默认模板，清空默认状态
          if (this.defaultTemplate === item.templateName) {
            this.defaultTemplate = ''
            _gridMemory.defaultTemplate = ''
          }

          // 统一存储格式
          sessionStorage.setItem(this.gridId, JSON.stringify({ gridMemory: _gridMemory }))

          API.post(saveUserMemory, {
            gridId: this.gridId,
            gridMemory: _gridMemory
          }).then((res) => {
            if (res.code === 200) {
              this.searchTemplates = _gridMemory.searchTemplates || []
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
            }
          })
        }
      })
    },
    setDefaultTemplate(item) {
      let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
      let _gridMemory = _gridInfo.gridMemory || _gridInfo
      _gridMemory.defaultTemplate = item.templateName
      this.defaultTemplate = item.templateName

      // 统一存储格式
      sessionStorage.setItem(this.gridId, JSON.stringify({ gridMemory: _gridMemory }))

      API.post(saveUserMemory, {
        gridId: this.gridId,
        gridMemory: _gridMemory
      }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('设置成功'), type: 'success' })
        }
      })
    },
    cancelDefaultTemplate() {
      let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
      let _gridMemory = _gridInfo.gridMemory || _gridInfo
      _gridMemory.defaultTemplate = ''
      this.defaultTemplate = ''

      // 统一存储格式
      sessionStorage.setItem(this.gridId, JSON.stringify({ gridMemory: _gridMemory }))

      API.post(saveUserMemory, {
        gridId: this.gridId,
        gridMemory: _gridMemory
      }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
        }
      })
    },
    saveAsTemplate() {
      // 获取当前搜索条件
      const searchRule = this.serializeSearchConditions()

      // 获取当前搜索状态的完整信息
      const currentSearchState = this.getCurrentSearchState()

      // 获取已有模板名称列表
      const templateNames = [
        ...this.defaultSearchTemplates.map((t) => t.templateName),
        ...this.searchTemplates.map((t) => t.templateName)
      ]

      this.saveDialogData = {
        gridId: this.gridId,
        searchRules: searchRule,
        searchConditions: currentSearchState.searchConditions,
        visibleConditions: currentSearchState.visibleConditions,
        conditionOrder: currentSearchState.conditionOrder,
        templateNames: templateNames
      }

      this.showSaveDialog = true
    },
    handleSaveDialogClose() {
      this.showSaveDialog = false
    },
    handleSaveSuccess(newTemplateName) {
      // 重新从 sessionStorage 获取最新的模板数据
      if (this.gridId) {
        let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
        let gridMemory = _gridInfo.gridMemory || _gridInfo

        // 更新模板列表，但保持当前模板选择不变
        this.updateTemplateState(gridMemory, true)
      }

      // 如果提供了新模板名称，自动切换到该模板
      if (newTemplateName) {
        // 使用 nextTick 确保模板列表更新后再切换
        this.$nextTick(() => {
          // 查找新保存的模板
          const newTemplate = this.allTemplateOptions.find(
            (t) => t.templateName === newTemplateName
          )
          if (newTemplate) {
            // 更新 sessionStorage 中的当前模板
            let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
            let _gridMemory = _gridInfo.gridMemory || _gridInfo
            _gridMemory.currentSearchTemplate = newTemplateName
            sessionStorage.setItem(this.gridId, JSON.stringify({ gridMemory: _gridMemory }))

            // 切换到新模板
            this.currentSearchTemplate = newTemplateName
            this.onTemplateChange(newTemplateName)
          } else {
            console.warn('未找到新保存的模板:', newTemplateName)
          }
        })
      }
    },

    // 重命名模板
    renameTemplate(item) {
      // 获取已有模板名称列表
      const templateNames = [
        ...this.defaultSearchTemplates.map((t) => t.templateName),
        ...this.searchTemplates.map((t) => t.templateName)
      ]

      this.renameDialogData = {
        gridId: this.gridId,
        currentName: item.templateName,
        templateNames: templateNames
      }

      this.showRenameDialog = true
    },

    handleRenameDialogClose() {
      this.showRenameDialog = false
    },

    handleRenameSuccess() {
      // 重新初始化搜索模板
      this.searchTemplateInit()
    },

    // 切换默认模板
    toggleDefaultTemplate(item) {
      if (item.isDefault) {
        // 取消默认
        this.cancelDefaultTemplate()
      } else {
        // 设为默认
        this.setDefaultTemplate(item)
      }
    },

    // 搜索条件设置相关方法
    openConditionSetting() {
      this.conditionDialogData = {
        availableConditions: this.availableConditions,
        visibleConditions: [...this.userSelectedConditions],
        conditionOrder: [...this.conditionOrder],
        requiredConditions: this.requiredConditions
      }
      this.showConditionDialog = true
    },

    handleConditionDialogClose() {
      this.showConditionDialog = false
    },

    handleConditionSettingSuccess(result) {
      this.userSelectedConditions = result.visibleConditions
      this.conditionOrder = result.conditionOrder

      // 保存用户设置（包括条件和顺序）
      this.saveConditionSettings(result.visibleConditions, result.conditionOrder)

      this.$emit('conditionSettingChange', {
        ...result,
        actualVisibleConditions: this.currentVisibleConditions
      })
    },
    // 序列化当前搜索条件
    serializeSearchConditions() {
      // 如果父组件传入了搜索表单数据，直接使用
      if (this.searchFormData && Object.keys(this.searchFormData).length > 0) {
        return this.searchFormData
      }

      // 否则通过事件通知父组件获取当前搜索条件
      let searchConditions = {}
      this.$emit('getSearchConditions', (conditions) => {
        searchConditions = conditions
      })

      return searchConditions
    },

    // 获取当前完整的搜索状态
    getCurrentSearchState() {
      // 获取当前搜索条件值
      const searchConditions = this.serializeSearchConditions()

      // 获取当前显示的搜索条件 - 保存字段名数组而不是完整对象数组
      const visibleConditions = (this.currentVisibleConditions || []).map((condition) =>
        this.getFieldName(condition)
      )

      // 获取搜索条件的显示顺序
      const conditionOrder = this.conditionOrder || []

      return {
        searchConditions,
        visibleConditions,
        conditionOrder
      }
    },

    // 应用搜索模板条件
    applySearchConditions(searchRule) {
      // 通过事件通知父组件应用搜索条件
      this.$emit('applySearchConditions', searchRule)

      // 如果有搜索条件，同时通知父组件恢复搜索表单的值
      if (searchRule && Object.keys(searchRule).length > 0) {
        this.$emit('restoreSearchFormData', searchRule)
      } else {
        console.log('搜索条件为空，不恢复表单数据')
      }
    },

    // 获取当前搜索条件（供外部调用）
    getCurrentSearchConditions() {
      return this.serializeSearchConditions()
    },

    // 设置搜索条件（供外部调用）
    setSearchConditions(conditions) {
      this.applySearchConditions(conditions)
    },

    // 清空当前选中的搜索模板
    clearCurrentTemplate() {
      this.currentSearchTemplate = ''
      if (this.gridId) {
        let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
        let _gridMemory = _gridInfo.gridMemory || _gridInfo
        _gridMemory.currentSearchTemplate = ''
        // 统一存储格式
        sessionStorage.setItem(this.gridId, JSON.stringify({ gridMemory: _gridMemory }))
      }
    },

    // 获取当前可见的搜索条件
    getCurrentVisibleConditions() {
      return this.currentVisibleConditions
    },

    // 切换展开/收起状态
    toggleExpanded() {
      this.isExpended = !this.isExpended
    },

    // 获取实际要显示的搜索条件
    getActualVisibleConditions() {
      // 如果用户没有自定义设置，显示所有可用条件
      if (this.userSelectedConditions.length === 0) {
        return this.availableConditions
      }

      // 如果有条件顺序设置，按照顺序排列
      if (this.conditionOrder && this.conditionOrder.length > 0) {
        const orderedConditions = []
        const userSelectedFields = this.userSelectedConditions.map((c) => this.getFieldName(c))

        // 按照 conditionOrder 的顺序添加条件，但只添加在 userSelectedConditions 中存在的条件
        this.conditionOrder.forEach((fieldName) => {
          // 只有当字段在 userSelectedConditions 中存在时才添加
          if (userSelectedFields.includes(fieldName)) {
            const fullCondition = this.availableConditions.find(
              (ac) => this.getFieldName(ac) === fieldName
            )
            if (fullCondition) {
              orderedConditions.push(fullCondition)
            }
          }
        })

        // 添加在 conditionOrder 中没有但在 userSelectedConditions 中存在的条件
        this.userSelectedConditions.forEach((condition) => {
          if (!this.conditionOrder.includes(this.getFieldName(condition))) {
            const fullCondition = this.availableConditions.find(
              (ac) => this.getFieldName(ac) === this.getFieldName(condition)
            )
            if (fullCondition) {
              orderedConditions.push(fullCondition)
            }
          }
        })

        return orderedConditions
      }

      // 根据用户设置过滤条件（保持原有逻辑作为后备）
      const selectedFields = this.userSelectedConditions.map((c) => this.getFieldName(c))
      return this.availableConditions.filter((condition) =>
        selectedFields.includes(this.getFieldName(condition))
      )
    },

    // 初始化搜索条件显示设置
    initializeConditionSettings() {
      // 从sessionStorage获取用户设置
      if (this.gridId) {
        const savedSettings = this.getConditionSettings()
        const savedOrder = this.getSavedConditionOrder()

        if (savedSettings && savedSettings.length > 0) {
          // 确保保存的设置中的条件在当前可用条件中存在
          this.userSelectedConditions = savedSettings.filter((saved) =>
            this.availableConditions.some(
              (available) => this.getFieldName(available) === this.getFieldName(saved)
            )
          )

          // 如果过滤后没有条件，则使用默认设置
          if (this.userSelectedConditions.length === 0) {
            this.userSelectedConditions = [...this.availableConditions]
          }
        } else {
          // 默认显示所有条件
          this.userSelectedConditions = [...this.availableConditions]
        }

        // 加载保存的条件顺序
        if (savedOrder && savedOrder.length > 0) {
          this.conditionOrder = savedOrder
        } else {
          // 如果没有保存的顺序，使用当前用户选择的条件顺序
          this.conditionOrder = this.userSelectedConditions.map((c) => this.getFieldName(c))
        }
      } else {
        // 没有gridId时，显示所有条件
        this.userSelectedConditions = [...this.availableConditions]
        this.conditionOrder = this.availableConditions.map((c) => this.getFieldName(c))
      }
    },

    // 获取保存的条件设置
    getConditionSettings() {
      if (!this.gridId) return []

      try {
        const gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
        const gridMemory = gridInfo.gridMemory || gridInfo
        return gridMemory.conditionSettings || []
      } catch (e) {
        console.warn('Failed to parse condition settings:', e)
        return []
      }
    },

    // 获取保存的条件顺序
    getSavedConditionOrder() {
      if (!this.gridId) return []

      try {
        const gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
        const gridMemory = gridInfo.gridMemory || gridInfo
        return gridMemory.conditionOrder || []
      } catch (e) {
        console.warn('Failed to parse condition order:', e)
        return []
      }
    },

    // 保存条件设置
    saveConditionSettings(conditions, conditionOrder) {
      if (!this.gridId) return

      try {
        let gridInfo = JSON.parse(sessionStorage.getItem(this.gridId) || '{}')
        let gridMemory = gridInfo.gridMemory || gridInfo
        gridMemory.conditionSettings = conditions
        gridMemory.visibleConditions = conditions // 保存可见条件

        // 同时保存条件顺序
        if (conditionOrder) {
          gridMemory.conditionOrder = conditionOrder
        }

        // 统一存储格式
        sessionStorage.setItem(this.gridId, JSON.stringify({ gridMemory: gridMemory }))

        // 异步保存到服务器
        API.post(saveUserMemory, {
          gridId: this.gridId,
          gridMemory: gridMemory
        }).catch((error) => {
          console.error('保存搜索条件设置失败:', error)
        })
      } catch (e) {
        console.warn('Failed to save condition settings:', e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.collapse-search-container {
  padding: 0;
  margin-bottom: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  // 操作按钮栏样式
  .action-bar {
    height: 44px;
    background: #ffffff;
    border: 1px solid #e9e9e9;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .left-actions {
      display: flex;
      align-items: center;
      gap: 20px;

      .toggle-tag {
        color: #2783fe;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        user-select: none;
        padding: 6px 9px;
        border-radius: 6px;
        transition: all 0.3s;
        border: 1px solid transparent;
        min-height: 30px;
        gap: 6px; // 文字和图标之间的间距

        &:hover {
          background-color: rgba(39, 131, 254, 0.08);
          border-color: rgba(39, 131, 254, 0.2);
        }

        span {
          line-height: 1;
          display: inline-flex;
          align-items: center;
          font-size: 14px;
          height: 14px; // 确保文字容器高度与字体大小一致
        }

        .mt-icons {
          font-size: 12px;
          transition: transform 0.3s ease;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 12px;
          height: 12px;
          line-height: 1;
          vertical-align: middle;
        }

        .expendIcon {
          margin-top: -3px;
          transform: rotate(180deg);
        }

        .unExpendIcon {
          margin-top: 3px;
          transform: rotate(0deg);
        }
      }

      .template-dropdown {
        min-width: 200px;
        position: relative;

        .custom-select-wrapper {
          position: relative;
          width: 100%;

          .custom-select-trigger {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 30px;
            padding: 0 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            color: #333;

            &:hover {
              border-color: #2783fe;
              box-shadow: 0 0 0 2px rgba(39, 131, 254, 0.1);
            }

            &.is-open {
              border-color: #2783fe;
              box-shadow: 0 0 0 3px rgba(39, 131, 254, 0.15);
            }

            .selected-text {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              color: #666666;
            }

            .mt-icons {
              font-size: 12px;
              transition: transform 0.3s ease;
              color: #999;

              &.rotated {
                transform: rotate(180deg);
              }
            }
          }

          .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            background: #ffffff;
            border: 1px solid #e9e9e9;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-height: 300px;
            overflow-y: auto;
            margin-top: 4px;

            .no-options {
              padding: 12px 14px;
              color: #999;
              text-align: center;
              font-size: 14px;
            }
          }
        }
      }

      .template-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 14px;
        width: 100%;
        transition: all 0.3s ease;
        cursor: pointer;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #f8f9ff;

          .action-buttons {
            opacity: 1;
          }
        }

        &.is-selected {
          background-color: #e6f7ff;
          color: #2783fe;
        }

        &.is-default {
          border-left: 3px solid #2783fe;
        }

        .template-name {
          flex: 1;
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }

        .template-actions {
          display: flex;
          align-items: center;
          gap: 10px;

          .default-tag {
            background: linear-gradient(135deg, #67c23a 0%, #5daf34 100%);
            color: #fff;
            font-size: 10px;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: 500;
          }

          .action-buttons {
            display: flex;
            align-items: center;
            gap: 6px;
            opacity: 0.6;
            transition: all 0.3s ease;

            .action-btn {
              padding: 4px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 13px;
              color: #999;
              transition: all 0.3s ease;

              &:hover {
                background-color: rgba(0, 0, 0, 0.08);
                transform: scale(1.1);
              }

              &.vxe-icon-star-fill {
                &.active {
                  color: #ffd700;
                }

                &:hover {
                  color: #ffd700;
                  background-color: rgba(255, 215, 0, 0.1);
                }
              }

              &.vxe-icon-edit:hover {
                color: #409eff;
                background-color: rgba(64, 158, 255, 0.1);
              }

              &.vxe-icon-delete:hover {
                color: #f56c6c;
                background-color: rgba(245, 108, 108, 0.1);
              }
            }
          }
        }
      }
    }
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;

    .action-btn {
      display: inline-flex;
      align-items: center;
      height: 30px;
      line-height: 28px;
      padding: 0 16px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      user-select: none;
      border: 1px solid transparent;
      box-sizing: border-box;
      transition: all 0.3s ease;
      white-space: nowrap;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
      }

      &[type='info'] {
        background: #ffffff;
        color: #666666;
        border-color: #d9d9d9;

        &:hover {
          border-color: #2783fe;
          color: #2783fe;
          background: #f8f9ff;
        }
      }

      &[type='primary'] {
        background: linear-gradient(135deg, #4a556b 0%, #2d323c 100%);
        color: #ffffff;
        border-color: #4a556b;

        &:hover {
          background: linear-gradient(135deg, #50596d 0%, #2d323c 100%);
          border-color: #515a6a;
        }
      }

      // 隐藏状态的按钮样式
      &.btn-hidden {
        opacity: 0;
        transform: translateX(20px);
        pointer-events: none;
        max-width: 0;
        padding: 0;
        margin: 0;
        overflow: hidden;
      }
    }
  }
}

// 搜索区域样式
.search-area {
  background: #fafafa;
  border: 1px solid #e9e9e9;
  border-top: none;
  border-radius: 0 0 8px 8px;
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  max-height: 0;
  opacity: 0;

  &.opend {
    padding: 18px 20px 0px 20px;
    max-height: 2000px;
    opacity: 1;
    border-top: 1px dashed #e9e9e9;
  }

  // 直接对 form 元素应用网格布局
  ::v-deep .mt-form {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0 18px;
    align-items: start;
    width: 100%;

    // 基础网格布局，每行5个
    & > .mt-form-item {
      width: 100%;
      min-width: 0; // 防止内容溢出
    }

    // 响应式布局
    @media (max-width: 1600px) {
      grid-template-columns: repeat(5, 1fr);
      gap: 0 16px;
    }

    @media (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 0 16px;
    }

    @media (max-width: 900px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 0 16px;
    }

    @media (max-width: 600px) {
      grid-template-columns: 1fr;
      gap: 0 16px;
    }
  }

  // 表单项样式
  ::v-deep .mt-form-item {
    margin-bottom: 0;
    width: 100%;
    min-height: 60px;
    display: flex;
    flex-direction: column;

    .mt-form-item-label {
      margin-bottom: 6px;
      flex-shrink: 0;

      .label {
        font-size: 13px;
        color: #666666;
        font-weight: 500;
        text-align: left;
        width: auto;
        line-height: 1.4;
        margin-bottom: 0;
      }
    }

    .mt-form-item-content {
      flex: 1;
      display: flex;
      align-items: flex-start;
    }

    .mt-input,
    .mt-select,
    .mt-date-picker,
    .mt-date-range-picker,
    .mt-input-number {
      width: 100%;

      // 下拉选择器特殊样式
      &.mt-select {
        .e-ddl {
          .e-input-group {
            height: 32px;
            border: none;

            .e-input {
              padding: 0 10px;
              line-height: 30px;
            }
          }
        }
      }

      // 数字输入框样式
      &.mt-input-number {
        input {
          height: 23px !important;
        }
      }
    }

    // 错误状态样式
    &.e-error {
      .e-input-group,
      .e-ddl {
        border-color: #f56c6c;

        &.e-input-focus,
        &.e-focused {
          box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.12);
        }
      }
    }
  }
}

// 展开状态样式
.collapse-search-container.isExpended {
  .search-area {
    &.opend {
      display: block;
      height: auto;
      max-height: 2000px;
      opacity: 1;
      transition: all 0.3s ease-in-out;

      // 展开状态下显示所有搜索条件
      ::v-deep .mt-form,
      ::v-deep .search-conditions-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 0 16px;

        // 显示所有搜索条件
        .mt-form-item {
          display: block;
        }
      }
    }
  }
}

// 收起状态样式
.collapse-search-container:not(.isExpended) {
  .search-area {
    max-height: 90px; // 显示一行搜索条件的高度
    opacity: 1;
    padding: 18px 20px 20px 20px;
    overflow: hidden;
    border-top: 1px dashed #e9e9e9;

    // 收起状态下的网格布局，确保只显示一行
    ::v-deep .mt-form,
    ::v-deep .search-conditions-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 0 16px;

      // 隐藏第6个及以后的搜索条件
      .mt-form-item:nth-child(n + 6) {
        display: none;
      }
    }
  }
}

// 确保表单项对齐
.collapse-search-container {
  ::v-deep .mt-form {
    .search-conditions-grid {
      .mt-form-item {
        // 确保所有表单项高度一致
        min-height: 60px;

        // 标签样式统一
        .mt-form-item-label {
          height: 20px;

          .label {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }
        }

        // 输入控件容器统一高度
        .mt-form-item-content {
          min-height: 32px;
        }
      }
    }
  }
}

// 动画关键帧
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
