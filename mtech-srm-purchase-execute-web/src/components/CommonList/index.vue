<template>
  <div class="common-list">
    <!-- 搜索栏 -->
    <div class="common-list__search" v-if="enableSearch">
      <collapse-search-new
        :enable-search-template="enableSearchTemplate"
        :enable-search-condition-setting="enableSearchConditionSetting"
        :grid-id="searchGridId"
        :available-conditions="availableConditions"
        :required-conditions="requiredConditions"
        @reset="handleReset"
        @search="handleSearch"
        @templateSearch="handleTemplateSearch"
        @getSearchConditions="handleGetSearchConditions"
        @applySearchConditions="handleApplySearchConditions"
        @restoreSearchFormData="handleRestoreSearchFormData"
        @visible-conditions-change="handleSearchToggle"
      >
        <template #default="{ visibleConditions }">
          <mt-form ref="searchFormRef" :model="searchForm" :rules="searchFormRules">
            <mt-form-item
              v-for="item in visibleConditions"
              :key="getFieldName(item)"
              :label="$t(item.label)"
              :prop="getFieldName(item)"
            >
              <component
                :is="getComponentType(item.type)"
                v-model="searchForm[getFieldName(item)]"
                v-bind="getComponentProps(item)"
                @change="handleSearchConditionChange($event, getFieldName(item), item)"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </collapse-search-new>
    </div>

    <!-- 表格容器 -->
    <div class="common-list__table">
      <sc-table
        ref="scTable"
        :key="`table-${tableRenderKey}`"
        v-bind="gridOptions"
        :grid-id="gridId"
        :table-data="tableData"
        :columns="columns"
        :loading="loading"
        :is-show-refresh-bth="showRefreshButton"
        :is-show-right-btn="showRightButton"
        :is-show-column-config="showColumnConfig"
        height="100%"
        @refresh="handleRefresh"
      >
        <!-- 工具栏按钮 -->
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            v-bind="item"
            v-permission="item.permission"
            size="small"
            @click="handleToolbarButtonClick(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>

        <!-- 自定义列插槽 -->
        <template v-for="(_, name) in $scopedSlots" #[name]="slotProps">
          <slot :name="name" v-bind="slotProps"></slot>
        </template>
      </sc-table>
    </div>

    <!-- 分页 -->
    <div class="common-list__pager" v-if="enablePagination">
      <vxe-pager
        :layouts="pagerLayouts"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="pageSizes"
        :perfect="false"
        background
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import CollapseSearchNew from '@/components/CollapseSearchNew'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { setupVXETableI18n } from './utils/vxe-i18n'

export default {
  name: 'CommonList',
  components: {
    CollapseSearchNew,
    ScTable,
    RemoteAutocomplete
  },
  props: {
    // 搜索相关
    enableSearch: {
      type: Boolean,
      default: true
    },
    enableSearchTemplate: {
      type: Boolean,
      default: true
    },
    enableSearchConditionSetting: {
      type: Boolean,
      default: true
    },
    searchGridId: {
      type: String,
      default: ''
    },
    availableConditions: {
      type: Array,
      default: () => []
    },
    requiredConditions: {
      type: Array,
      default: () => []
    },
    searchFormRules: {
      type: Object,
      default: () => ({})
    },

    // 表格相关
    gridId: {
      type: String,
      default: ''
    },
    columns: {
      type: Array,
      default: () => []
    },
    gridOptions: {
      type: Object,
      default: () => ({})
    },
    toolbar: {
      type: Array,
      default: () => []
    },
    // ScTable相关配置
    showRefreshButton: {
      type: Boolean,
      default: false
    },
    showRightButton: {
      type: Boolean,
      default: true
    },
    showColumnConfig: {
      type: Boolean,
      default: true
    },

    // 分页相关
    enablePagination: {
      type: Boolean,
      default: true
    },
    pageSizes: {
      type: Array,
      default: () => [20, 50, 200, 500, 1000]
    },
    defaultPageSize: {
      type: Number,
      default: 50
    },
    // 分页布局配置
    pagerLayouts: {
      type: Array,
      default: () => [
        'Total',
        'PrevJump',
        'PrevPage',
        'JumpNumber',
        'NextPage',
        'NextJump',
        'FullJump',
        'Sizes'
      ]
    }
  },
  data() {
    return {
      searchForm: {},
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSize: this.defaultPageSize,
      total: 0,
      // 窗口大小变化防抖定时器
      resizeTimer: null,
      // 搜索栏高度监听器
      searchHeightObserver: null,
      // 表格布局监控定时器
      layoutMonitorTimer: null,
      // 表格重新渲染key
      tableRenderKey: 0,
      // 高度计算防抖标志
      isCalculatingHeight: false,
      // 记录搜索栏当前高度，用于判断是否真的需要重新计算
      lastSearchBarHeight: 0
    }
  },
  computed: {
    // 保留computed用于未来扩展
  },
  created() {
    // 设置 VXETable 中文国际化
    setupVXETableI18n()
  },
  mounted() {
    // 设置 VXETable 中文国际化
    setupVXETableI18n()

    // 初始化表格高度计算
    this.$nextTick(() => {
      // 设置搜索栏高度监听器
      this.setupSearchHeightObserver()

      // 计算并设置表格高度
      this.calculateAndSetTableHeight()

      // 延迟再次调用，确保所有组件都已渲染完成
      setTimeout(() => {
        this.calculateAndSetTableHeight()
      }, 100)
    })

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleWindowResize)
  },
  beforeDestroy() {
    // 清理事件监听
    window.removeEventListener('resize', this.handleWindowResize)

    // 清理防抖定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
      this.resizeTimer = null
    }

    // 清理搜索栏高度监听器
    if (this.searchHeightObserver) {
      this.searchHeightObserver.disconnect()
      this.searchHeightObserver = null
    }
  },
  methods: {
    // 搜索相关方法
    getFieldName(item) {
      return item.field || item.prop
    },
    getComponentType(type) {
      const typeMap = {
        input: 'mt-input',
        select: 'mt-select',
        multiSelect: 'mt-multi-select',
        number: 'mt-input-number',
        date: 'mt-date-picker',
        dateRange: 'mt-date-range-picker',
        remoteAutocomplete: 'RemoteAutocomplete'
      }
      return typeMap[type] || 'mt-input'
    },
    getComponentProps(item) {
      const props = {
        placeholder: `请输入${item.label}`,
        showClearButton: true
      }

      if (['select', 'multiSelect'].includes(item.type) && item.options) {
        props.dataSource = item.options
        props.fields = { text: 'text', value: 'value' }
        props.placeholder = `请选择${item.label}`
      }

      if (item.type === 'number') {
        props.showSpinButton = false
      }

      if (item.type === 'dateRange') {
        props.placeholder = `请选择${item.label}范围`
      }

      if (item.type === 'remoteAutocomplete') {
        props.placeholder = `请选择${item.label}`
      }

      return { ...props, ...(item.props || {}) }
    },

    handleReset() {
      this.searchForm = {}
      this.$emit('reset')
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.$emit('search', {
        searchForm: this.searchForm,
        page: this.currentPage,
        pageSize: this.pageSize
      })
    },
    handleTemplateSearch(template) {
      this.$emit('templateSearch', template)
      // 不在这里调用 handleSearch()，让父组件决定是否需要搜索
      // this.handleSearch()
    },
    handleGetSearchConditions(callback) {
      // 过滤掉空值
      const conditions = Object.keys(this.searchForm).reduce((acc, key) => {
        const value = this.searchForm[key]
        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value
        }
        return acc
      }, {})
      callback(conditions)
      this.$emit('getSearchConditions', conditions)
    },
    handleApplySearchConditions(conditions) {
      this.$emit('applySearchConditions', conditions)
    },
    handleRestoreSearchFormData(conditions) {
      // 先重置搜索表单
      this.searchForm = {}

      // 恢复保存的搜索条件值
      if (conditions && Object.keys(conditions).length > 0) {
        Object.keys(conditions).forEach((key) => {
          // 使用 Vue.set 确保新属性也是响应式的
          this.$set(this.searchForm, key, conditions[key])
        })
      }

      this.$emit('restoreSearchFormData', conditions)
    },

    // 处理搜索条件变化事件
    handleSearchConditionChange(value, fieldName, item) {
      // 先执行原有的 onChange 回调（如果存在）
      if (item.onChange && typeof item.onChange === 'function') {
        item.onChange(value, fieldName)
      }

      // 向父组件传递 change 事件
      this.$emit('searchConditionChange', {
        value,
        fieldName,
        item,
        searchForm: this.searchForm
      })
    },

    // 表格相关方法
    handleToolbarButtonClick(item) {
      // 兼容两种调用方式：直接传递item对象或传递包含code的对象
      const toolbarItem = item.code ? item : this.toolbar.find((btn) => btn.code === item.code)
      if (toolbarItem) {
        this.$emit('toolbarClick', toolbarItem)
      }
    },
    handleRefresh() {
      this.$emit('refresh')
      // 如果没有监听refresh事件，则触发搜索
      if (!this.$listeners.refresh) {
        this.handleSearch()
      }
    },

    // 分页相关方法
    handlePageChange({ currentPage }) {
      this.currentPage = currentPage
      this.$emit('pageChange', {
        page: currentPage,
        pageSize: this.pageSize,
        searchForm: this.searchForm
      })
    },
    handleSizeChange({ pageSize }) {
      this.pageSize = pageSize
      this.currentPage = 1
      this.$emit('pageSizeChange', {
        page: this.currentPage,
        pageSize,
        searchForm: this.searchForm
      })
    },

    // 公共方法
    setTableData(data) {
      this.tableData = data
    },
    setLoading(status) {
      this.loading = status
    },
    setPagination(pagination) {
      const { total, current, size } = pagination
      this.total = total || 0
      if (current) this.currentPage = current
      if (size) this.pageSize = size
    },
    setSearchForm(form) {
      this.searchForm = form
    },
    getSelectedRows() {
      return this.$refs.scTable.$refs.xGrid.getCheckboxRecords()
    },
    // 获取ScTable实例，用于调用其他方法
    getScTableRef() {
      return this.$refs.scTable
    },
    // 获取内部vxe-grid实例
    getGridRef() {
      return this.$refs.scTable.$refs.xGrid
    },

    // 公共方法：刷新表格布局
    refreshTableLayout() {
      console.log('开始刷新表格布局')
      this.calculateAndSetTableHeight()
    },

    // 计算并设置表格高度的核心方法
    calculateAndSetTableHeight() {
      // 防止重复计算
      if (this.isCalculatingHeight) {
        console.log('高度计算正在进行中，跳过重复计算')
        return
      }

      this.isCalculatingHeight = true

      this.$nextTick(() => {
        try {
          const scTableRef = this.getScTableRef()
          if (!scTableRef) {
            console.warn('ScTable引用不存在，跳过高度计算')
            this.isCalculatingHeight = false
            return
          }

          // 计算表格应有的高度
          const calculatedHeight = this.calculateTableHeight()

          if (calculatedHeight > 0) {
            // 设置计算后的高度
            scTableRef.containerHeight = `${calculatedHeight}px`
            console.log(`表格高度设置为: ${calculatedHeight}px`)

            // 触发表格重新计算
            this.triggerTableRecalculation()
          } else {
            console.warn('计算的表格高度无效:', calculatedHeight)
          }
        } catch (error) {
          console.error('表格高度计算失败:', error)
        } finally {
          // 延迟重置标志，避免过于频繁的计算
          setTimeout(() => {
            this.isCalculatingHeight = false
          }, 200)
        }
      })
    },

    // 计算表格高度：视口高度 - 搜索栏高度 - 按钮栏高度 - 分页器高度
    calculateTableHeight() {
      // 获取视口高度
      const viewportHeight = window.innerHeight

      // 获取各部分高度
      const searchHeight = this.getSearchBarHeight()
      const buttonHeight = this.getButtonBarHeight()
      const pagerHeight = this.getPagerHeight()
      const otherHeight = this.getOtherElementsHeight()

      // 计算表格可用高度
      const tableHeight =
        viewportHeight - searchHeight - buttonHeight - pagerHeight - otherHeight + 15

      console.log('高度计算详情:', {
        视口高度: viewportHeight,
        搜索栏高度: searchHeight,
        按钮栏高度: buttonHeight,
        分页器高度: pagerHeight,
        其他元素高度: otherHeight,
        计算的表格高度: tableHeight
      })

      return Math.max(tableHeight, 200) // 最小高度200px
    },

    // 获取搜索栏高度
    getSearchBarHeight() {
      if (!this.enableSearch) return 0

      const searchEl = this.$el.querySelector('.common-list__search')
      return searchEl ? searchEl.offsetHeight : 0
    },

    // 获取按钮栏高度（表格工具栏）
    getButtonBarHeight() {
      const scTableRef = this.getScTableRef()
      if (!scTableRef) return 0

      // 通过ScTable的$el查找工具栏（ColumnConfig组件渲染的.table-tool-bar）
      let toolbarEl = scTableRef.$el?.querySelector('.table-tool-bar')

      // 如果找不到，尝试通过vxeTableContainer查找
      if (!toolbarEl && scTableRef.$refs.vxeTableContainer) {
        toolbarEl = scTableRef.$refs.vxeTableContainer.querySelector('.table-tool-bar')
      }

      // 如果还是找不到，全局查找
      if (!toolbarEl) {
        toolbarEl = document.querySelector('.table-tool-bar')
      }

      return toolbarEl ? toolbarEl.offsetHeight : 40 // 默认工具栏高度40px
    },

    // 获取分页器高度
    getPagerHeight() {
      if (!this.enablePagination) return 0

      const pagerEl = this.$el.querySelector('.common-list__pager')
      return pagerEl ? pagerEl.offsetHeight : 56 // 默认分页器高度
    },

    // 获取其他元素高度（页面边距、padding等）
    getOtherElementsHeight() {
      let totalHeight = 0

      // 页面顶部导航、面包屑等固定高度
      const headerHeight = 60
      totalHeight += headerHeight

      // 检测并计算 Tab 高度
      const tabHeight = this.getTabHeight()
      totalHeight += tabHeight

      // 页面内边距
      const paddingHeight = 40
      totalHeight += paddingHeight

      // 预留空间
      const reservedHeight = 20
      totalHeight += reservedHeight

      console.log('其他元素高度详情:', {
        页面头部: headerHeight,
        Tab高度: tabHeight,
        页面边距: paddingHeight,
        预留空间: reservedHeight,
        总计: totalHeight
      })

      return totalHeight
    },

    // 获取 Tab 高度（兼容有 tab 的情况）
    getTabHeight() {
      let tabHeight = 0

      try {
        // 方法1: 查找父级容器中的 mt-tabs 组件
        let currentElement = this.$el
        let searchDepth = 0
        const maxSearchDepth = 5 // 限制搜索深度，避免无限循环

        while (currentElement && currentElement.parentElement && searchDepth < maxSearchDepth) {
          currentElement = currentElement.parentElement
          searchDepth++

          // 查找明确的 Tab 相关元素，避免误判
          const tabElements = [
            currentElement.querySelector('.mt-tabs'),
            currentElement.querySelector('.vxe-tabs'),
            currentElement.querySelector('.tab-container'),
            currentElement.querySelector('.tab-wrap'),
            currentElement.querySelector('.tabs-overall')
          ].filter(Boolean)

          // 额外检查：确保找到的元素确实是 Tab 组件
          const validTabElements = tabElements.filter((el) => {
            // 检查元素是否包含 Tab 相关的子元素或属性
            return (
              el.querySelector('.tab-item, .tab-pane, [role="tab"], [role="tabpanel"]') ||
              el.hasAttribute('tab-id') ||
              el.classList.contains('mt-tabs') ||
              el.classList.contains('vxe-tabs')
            )
          })

          if (validTabElements.length > 0) {
            // 取最大的 tab 高度
            tabHeight = Math.max(...validTabElements.map((el) => el.offsetHeight))
            console.log(`在第${searchDepth}层找到有效Tab元素，高度: ${tabHeight}px`)
            break
          }
        }

        // 方法2: 如果没找到，尝试通过 Vue 组件树查找
        if (tabHeight === 0) {
          tabHeight = this.getTabHeightFromVueTree()
        }

        // 方法3: 如果仍然没找到，且确实在 Tab 环境中，才使用默认值
        if (tabHeight === 0) {
          // 只有在明确检测到 Tab 环境时才添加默认高度
          const isInTabContext = this.detectTabContext()
          if (isInTabContext) {
            tabHeight = 48 // 默认 tab 高度，使用更保守的值
            console.log('通过上下文检测到Tab环境，使用默认高度:', tabHeight)
          } else {
            console.log('未检测到Tab环境，Tab高度为0')
          }
        }
      } catch (error) {
        console.warn('获取Tab高度时出错:', error)
        tabHeight = 0
      }

      return tabHeight
    },

    // 通过 Vue 组件树查找 Tab 高度
    getTabHeightFromVueTree() {
      let tabHeight = 0

      try {
        // 向上遍历 Vue 组件树
        let parent = this.$parent
        let searchDepth = 0
        const maxSearchDepth = 10

        while (parent && searchDepth < maxSearchDepth) {
          searchDepth++

          // 检查父组件是否包含明确的 Tab 相关特征
          if (
            parent.$options.name &&
            (parent.$options.name.toLowerCase().includes('tabs') ||
              parent.$options.name === 'CommonListTabsDemo' ||
              parent.$options.name === 'TabHeightTestDemo')
          ) {
            // 尝试获取父组件的 DOM 元素中的 tab
            if (parent.$el) {
              const tabEl = parent.$el.querySelector('.mt-tabs, .vxe-tabs, [class*="tab"]')
              if (tabEl) {
                tabHeight = tabEl.offsetHeight
                console.log(
                  `从Vue组件树找到Tab，组件名: ${parent.$options.name}，高度: ${tabHeight}px`
                )
                break
              }
            }
          }

          parent = parent.$parent
        }
      } catch (error) {
        console.warn('从Vue组件树获取Tab高度时出错:', error)
      }

      return tabHeight
    },

    // 检测是否在 Tab 上下文中
    detectTabContext() {
      try {
        // 只检查明确的 Tab 相关路径，避免误判
        const routeName = this.$route?.name
        const routePath = this.$route?.path

        // 只检查明确包含 "tabs" 的路由名称（复数形式，更精确）
        if (routeName && routeName.toLowerCase().includes('tabs')) {
          return true
        }

        // 只检查明确的 tabs-demo 路径
        if (routePath && routePath.includes('tabs-demo')) {
          return true
        }

        // 检查页面 URL 中是否包含明确的 tabs 标识
        const url = window.location.href
        if (url.includes('tabs-demo')) {
          return true
        }

        return false
      } catch (error) {
        console.warn('检测Tab上下文时出错:', error)
        return false
      }
    },

    // 触发表格重新计算
    triggerTableRecalculation() {
      const gridRef = this.getGridRef()
      if (gridRef && gridRef.recalculate) {
        gridRef.recalculate()
        console.log('表格重新计算完成')
      }
    },

    // 处理搜索区域展开/收起状态变化
    handleSearchToggle() {
      console.log('搜索栏状态变化，重新计算表格高度')

      // 延迟执行，等待CSS动画完成
      setTimeout(() => {
        // 更新记录的搜索栏高度
        const searchEl = this.$el.querySelector('.common-list__search')
        if (searchEl) {
          this.lastSearchBarHeight = searchEl.offsetHeight
        }
        this.calculateAndSetTableHeight()
      }, 350) // 比CSS动画时间稍长一点
    },

    // 处理窗口大小变化
    handleWindowResize() {
      // 防抖处理
      clearTimeout(this.resizeTimer)
      this.resizeTimer = setTimeout(() => {
        console.log('窗口大小变化，重新计算表格高度')
        this.calculateAndSetTableHeight()
      }, 100)
    },

    // 设置搜索栏高度监听器
    setupSearchHeightObserver() {
      const searchEl = this.$el.querySelector('.common-list__search')
      if (!searchEl || this.searchHeightObserver) return

      // 记录初始高度
      this.lastSearchBarHeight = searchEl.offsetHeight

      // 使用ResizeObserver监听搜索栏高度变化
      if (window.ResizeObserver) {
        this.searchHeightObserver = new ResizeObserver(() => {
          // 检查高度是否真的发生了变化
          const currentHeight = searchEl.offsetHeight
          if (currentHeight !== this.lastSearchBarHeight) {
            const oldHeight = this.lastSearchBarHeight
            this.lastSearchBarHeight = currentHeight
            console.log('搜索栏高度发生变化，重新计算表格高度', {
              旧高度: oldHeight,
              新高度: currentHeight
            })

            // 防抖处理，避免频繁触发
            clearTimeout(this.resizeTimer)
            this.resizeTimer = setTimeout(() => {
              this.calculateAndSetTableHeight()
            }, 100)
          }
        })

        this.searchHeightObserver.observe(searchEl)
      } else {
        // 降级方案：使用MutationObserver，但增加高度变化检查
        this.searchHeightObserver = new MutationObserver(() => {
          // 检查高度是否真的发生了变化
          const currentHeight = searchEl.offsetHeight
          if (currentHeight !== this.lastSearchBarHeight) {
            const oldHeight = this.lastSearchBarHeight
            this.lastSearchBarHeight = currentHeight
            console.log('搜索栏高度发生变化（MutationObserver），重新计算表格高度', {
              旧高度: oldHeight,
              新高度: currentHeight
            })

            clearTimeout(this.resizeTimer)
            this.resizeTimer = setTimeout(() => {
              this.calculateAndSetTableHeight()
            }, 100)
          }
        })

        this.searchHeightObserver.observe(searchEl, {
          attributes: true,
          childList: true,
          subtree: true,
          attributeFilter: ['class', 'style']
        })
      }
    },

    // 最后的备用方案：强制重新渲染表格（仅在必要时使用）
    forceReRenderTable() {
      console.log('⚠️ 强制重新渲染表格（会导致闪烁）')
      this.tableRenderKey += 1
      console.log('表格重新渲染完成，新key:', this.tableRenderKey)
    }
  }
}
</script>

<style lang="scss" scoped>
.common-list {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 10px 0;
  box-sizing: border-box;

  &__search {
    flex-shrink: 0;
  }

  &__table {
    min-height: 0;
    overflow: hidden;
  }

  &__pager {
    flex-shrink: 0;

    // 分页器样式优化，与查询按钮颜色保持一致
    ::v-deep .vxe-pager {
      // 通用分页按钮样式 - 支持多种类名
      .vxe-pager--btn,
      .vxe-pager--btn-wrapper,
      .vxe-pager--num-btn,
      .vxe-pager--jump-prev,
      .vxe-pager--jump-next,
      .vxe-pager--prev-btn,
      .vxe-pager--next-btn,
      button {
        color: #666666 !important;
        transition: all 0.3s ease !important;

        &:hover {
          border-color: #4a556b !important;
          color: #4a556b !important;
          background: #f8f9ff !important;
        }

        &:not(.is--disabled):hover {
          border-color: #4a556b !important;
          color: #4a556b !important;
          background: #f8f9ff !important;
        }
      }

      // 当前页按钮样式 - 支持多种激活状态类名
      .vxe-pager--btn.is--active,
      .vxe-pager--num-btn.is--active,
      .is--active,
      .active,
      button.is--active,
      button.active {
        background: linear-gradient(135deg, #4a556b 0%, #2d323c 100%) !important;
        color: #ffffff !important;
        border-color: #4a556b !important;

        &:hover {
          background: linear-gradient(135deg, #50596d 0%, #2d323c 100%) !important;
          border-color: #515a6a !important;
        }
      }

      // 禁用状态按钮
      .vxe-pager--btn.is--disabled,
      .vxe-pager--num-btn.is--disabled,
      .is--disabled,
      .disabled,
      button:disabled,
      button.is--disabled {
        background: #f5f5f5 !important;
        color: #cccccc !important;
        border-color: #e8e8e8 !important;
        cursor: not-allowed !important;

        &:hover {
          background: #f5f5f5 !important;
          color: #cccccc !important;
          border-color: #e8e8e8 !important;
        }
      }

      // 页码输入框样式
      .vxe-pager--jump-number,
      .vxe-pager--goto,
      input[type='text'],
      input[type='number'] {
        &:focus {
          border-color: #4a556b !important;
          box-shadow: 0 0 0 2px rgba(74, 85, 107, 0.12) !important;
        }
      }

      // 每页显示数量选择器样式
      .vxe-pager--sizes,
      .vxe-pager--size-select {
        .vxe-select,
        select {
          &:hover {
            border-color: #4a556b !important;
          }

          &:focus,
          &.is--focus {
            border-color: #4a556b !important;
            box-shadow: 0 0 0 2px rgba(74, 85, 107, 0.12) !important;
          }
        }
      }

      // 分页信息文字颜色
      .vxe-pager--total,
      .vxe-pager--jump-prev,
      .vxe-pager--jump-next,
      .vxe-pager--info {
        color: #666666 !important;
      }
    }
  }
}
</style>
