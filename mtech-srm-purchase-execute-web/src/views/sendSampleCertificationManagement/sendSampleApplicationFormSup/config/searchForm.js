/**
 * 搜索表单配置
 */
import { i18n } from '@/main.js'
import { statusOptions } from './index'

export const getSearchFormItems = () => [
  {
    label: i18n.t('送样单号'),
    prop: 'applyNo',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('名称'),
    prop: 'applyName',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  // {
  //   label: i18n.t('申请类型'),
  //   prop: 'applyType',
  //   component: 'mt-input',
  //   props: {
  //     placeholder: i18n.t('请输入'),
  //     showClearButton: true
  //   }
  // },
  {
    label: i18n.t('公司'),
    prop: 'companyCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('物料编码'),
    prop: 'materialCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  // {
  //   label: i18n.t('供应商编码'),
  //   prop: 'supplierCode',
  //   component: 'mt-input',
  //   props: {
  //     placeholder: i18n.t('请输入'),
  //     showClearButton: true
  //   }
  // },
  {
    label: i18n.t('承诺送样日期'),
    prop: 'promiseDeliveryDate',
    component: 'mt-date-range-picker',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('实际送样日期'),
    prop: 'actualDeliveryDate',
    component: 'mt-date-range-picker',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true
    }
  },
  // {
  //   label: i18n.t('申请人姓名'),
  //   prop: 'applicantName',
  //   component: 'mt-input',
  //   props: {
  //     placeholder: i18n.t('请输入'),
  //     showClearButton: true
  //   }
  // },
  // {
  //   label: i18n.t('申请人联系方式'),
  //   prop: 'applicantPhone',
  //   component: 'mt-input',
  //   props: {
  //     placeholder: i18n.t('请输入'),
  //     showClearButton: true
  //   }
  // },
  {
    label: i18n.t('状态'),
    prop: 'status',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      dataSource: statusOptions
    }
  }
]
