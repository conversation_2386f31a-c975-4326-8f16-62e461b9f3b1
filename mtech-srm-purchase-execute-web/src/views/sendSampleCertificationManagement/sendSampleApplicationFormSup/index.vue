<!-- 供方-送样申请单 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="f43f74e2-b5dd-44d8-a1d5-ff418d3bc055"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #applyNo="{ row, column }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleCheck(row)">
            {{ row[column.field] }}
          </span>
        </div>
      </template>

      <template #authApplyNo="{ row, column }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleAuthCheck(row)">
            {{ row[column.field] }}
          </span>
        </div>
      </template>

      <!-- <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template> -->
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, toolbar } from './config/index'
import { getSearchFormItems } from './config/searchForm'
import dayjs from 'dayjs'

// 操作类型常量
const OPERATION_TYPES = {
  CONFIRM: 'confirm'
}

// 操作配置
const OPERATION_CONFIG = {
  [OPERATION_TYPES.CONFIRM]: {
    title: '提示',
    message: '确认选中的数据？',
    api: 'confirmSendSampleApplicationFormApi',
    successMessage: '确认成功！',
    requireSelection: true
  }
}

export default {
  name: 'SendSampleApplicationForm',
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      columns: columnData,
      toolbar,
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems()
      // 为创建时间、更新时间字段添加onChange事件处理
      const dateFields = [
        'promiseSendSampleDate',
        'actualSendSampleDate',
        'createTime',
        'updateTime'
      ]
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },

  created() {
    this.getTableData()
  },

  methods: {
    handleCheck(row) {
      this.$router.push({
        name: 'send-sample-management-detail-sup',
        query: {
          id: row.id,
          type: 'check',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleAuthCheck(row) {
      this.$router.push({
        name: 'certification-management-detail',
        query: {
          id: row.authApplyDataId,
          type: 'check',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}S`] = null
        this.searchFormModel[`${field}E`] = null
        return
      }

      this.searchFormModel[`${field}S`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}E`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        if (params?.actualDeliveryDate?.length > 1) {
          params.actualDeliveryDate[0] = dayjs(params.actualDeliveryDate[0]).format('YYYY-MM-DD')
          params.actualDeliveryDate[1] = dayjs(params.actualDeliveryDate[1]).format('YYYY-MM-DD')
        }

        if (params?.promiseDeliveryDate?.length > 1) {
          params.promiseDeliveryDate[0] = dayjs(params.promiseDeliveryDate[0]).format('YYYY-MM-DD')
          params.promiseDeliveryDate[1] = dayjs(params.promiseDeliveryDate[1]).format('YYYY-MM-DD')
        }

        const res = await this.$API.sendSampleCertification.pageSendSampleApplicationFormSupApi(
          params
        )

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const operation = OPERATION_CONFIG[item.code]

      if (operation?.requireSelection && selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }

      let ids = selectedRecords.map((item) => item.id)

      const res = await this.$API.sendSampleCertification.plmSampleApplySubmitData({
        ids
      })
      if (res.code == 200 && res.data) {
        this.$toast({
          content: this.$t(`提交成功`),
          type: 'success'
        })

        this.getTableData()
      }
    },

    /**
     * 处理操作
     * @param {Array} selectedRecords - 选中的记录
     * @param {string} type - 操作类型
     * @param {Object} extraParams - 额外参数
     */
    handleOperate(selectedRecords, type, extraParams = {}) {
      const operation = OPERATION_CONFIG[type]
      if (!operation) {
        this.$toast({ content: this.$t('不支持的操作类型'), type: 'error' })
        return
      }

      this.$dialog({
        data: {
          title: this.$t(operation.title),
          message: this.$t(operation.message),
          type: 'warning'
        },
        success: async () => {
          try {
            const params = {
              ids: selectedRecords.map((item) => item.id),
              ...extraParams
            }

            const res = await this.$API.sendSampleCertification[operation.api](params)
            if (res.code === 200) {
              this.$toast({
                content: this.$t(operation.successMessage),
                type: 'success'
              })
              await this.handleSearch()
            } else {
              throw new Error(res || this.$t(`${operation.title}失败`))
            }
          } catch (error) {
            console.error(`${operation.title}失败:`, error)
            this.$toast({
              content: error || this.$t(`${operation.title}失败`),
              type: 'error'
            })
          }
        }
      })
    }
  }
}
</script>
