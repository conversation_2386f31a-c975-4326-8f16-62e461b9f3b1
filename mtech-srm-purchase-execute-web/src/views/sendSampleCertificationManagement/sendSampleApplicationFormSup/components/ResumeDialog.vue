<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
    height="910"
    width="1710"
  >
    <div class="dialog-content">
      <sc-table
        ref="sctableRef"
        grid-id="dbc28221-4a59-4184-9eb2-46abe4b96497"
        :fix-height="700"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        align="center"
        keep-source
        :sortable="false"
        :is-show-right-btn="false"
        :is-show-refresh-bth="false"
      >
        <template #fileDefault="{ row }">
          <div>
            <span style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
              {{ $t('查看附件') }}
            </span>
          </div>
        </template>
      </sc-table>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],

      loading: false,
      tableData: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    currentId() {
      return this.modalData.id
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          title: this.$t('模具履历表'),
          children: [
            {
              type: 'seq',
              title: this.$t('序号'),
              width: 50,
              fixed: 'left',
              align: 'center'
            },
            {
              field: 'mouldStatusDesc',
              title: this.$t('模具状态')
            },
            {
              field: 'mouldContentDesc',
              title: this.$t('内容摘要')
            },
            {
              field: 'statusChageTime',
              title: this.$t('时间')
            },
            {
              field: 'productionQuantity',
              title: this.$t('生产数量累计')
            },
            {
              field: 'changeDesc',
              title: this.$t('变更说明')
            },
            {
              field: 'currentStorageAddress',
              title: this.$t('当前存放地址')
            },
            {
              field: 'file',
              title: this.$t('附件'),
              slots: {
                default: 'fileDefault'
              }
            },
            {
              field: 'remark',
              title: this.$t('备注')
            }
          ]
        }
      ]
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getTableData()
  },
  methods: {
    handleClick(row) {
      this.$dialog({
        modal: () => import('./FileManage.vue'),
        data: {
          title: this.$t('查看附件'),
          id: row.id,
          type: 'download'
        }
      })
    },
    getTableData() {
      let params = {
        mouldMainId: this.currentId
      }
      this.$API.moldManagement.getByMouldMainIdApi(params).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    // padding: 0;
    .dialog-content {
      padding: 20px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
