<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="[dataInfo]"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :fix-height="120"
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'

export default {
  name: 'ItemTab',
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        shippedQuantity: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        // {
        //   type: 'checkbox',
        //   width: 50,
        //   fixed: 'left',
        //   align: 'center'
        // },
        // {
        //   type: 'seq',
        //   title: this.$t('序号'),
        //   width: 50,
        //   fixed: 'left',
        //   align: 'center'
        // },
        // {
        //   field: 'deliveryPlanNo',
        //   title: this.$t('送货计划单号')
        // },
        {
          field: 'materialCode',
          title: this.$t('物料编码')
        },
        {
          field: 'materialName',
          title: this.$t('物料名称')
        },
        {
          field: 'materialClassification',
          title: this.$t('物料分类')
        },
        {
          field: 'categoryName',
          title: this.$t('采购品类')
        },
        {
          field: 'materialDesc',
          title: this.$t('物料长描述')
        },
        {
          field: 'sourceName',
          title: this.$t('来源')
        },
        {
          field: 'safeRegulationFlag',
          title: this.$t('是否需要安规')
        },
        {
          field: 'safetyStandardAuthRequirement',
          title: this.$t('安规认证要求')
        },
        {
          field: 'sampleItemModel',
          title: this.$t('型号规格')
        },

        {
          field: 'quantity',
          title: this.$t('样品数量')
        },
        {
          field: 'sampleDeliveryDate',
          title: this.$t('样品交付日期')
        },
        {
          field: 'urgentDeliveryDate',
          title: this.$t('紧急交付日期')
        },
        {
          field: 'sampleConfirmFlag',
          width: 130,
          title: this.$t('是否需要样品确认')
          // editRender: {},
          // slots: {
          //   default: ({ row }) => {
          //     return [
          //       <mt-select
          //         v-model={row.chargeFlag}
          //         data-source={[
          //           { value: '是', text: '是', cssClass: 'col-active' },
          //           { value: '否', text: '否', cssClass: 'col-active' }
          //         ]}
          //         fields={{ text: 'text', value: 'value' }}
          //         show-clear-button={true}
          //         allow-filtering={true}
          //         filter-type='Contains'
          //       />
          //     ]
          //   }
          // }
        },
        {
          field: 'sampleFactory',
          title: this.$t('送样厂家')
        },
        {
          field: 'promiseDeliveryDate',
          width: 120,
          title: this.$t('承诺送样日期')
          // editRender: {},
          // slots: {
          //   default: ({ row }) => {
          //     return [
          //       <mt-date-picker
          //         open-on-focus={true}
          //         format={'yyyy-MM-dd'}
          //         value-format={'yyyy-MM-dd'}
          //         show-clear-button={false}
          //         v-model={row.promiseDeliveryDate}
          //         placeholder={this.$t('请选择')}
          //         clearable></mt-date-picker>
          //     ]
          //   }
          // }
        },

        // <vxe-date-picker v-model="val1" placeholder="默认尺寸"></vxe-date-picker>

        {
          field: 'chargeFlag',
          title: this.$t('是否收费')
          // editRender: {},
          // slots: {
          //   default: ({ row }) => {
          //     return [
          //       <mt-select
          //         v-model={row.chargeFlag}
          //         data-source={[
          //           { value: '是', text: '是', cssClass: 'col-active' },
          //           { value: '否', text: '否', cssClass: 'col-active' }
          //         ]}
          //         fields={{ text: 'text', value: 'value' }}
          //         show-clear-button={true}
          //         allow-filtering={true}
          //         filter-type='Contains'
          //       />
          //     ]
          //   }
          // }
        },

        {
          field: 'sapPurchaseNo',
          title: this.$t('SAP申请单号')
        },
        {
          field: 'sampleReceiveDate',
          title: this.$t('样品签收日期')
        }

        // {
        //   field: 'shippedQuantity',
        //   title: this.$t('发货数量'),
        //   editRender: {},
        //   slots: {
        //     edit: ({ row }) => {
        //       return [
        //         <vxe-input
        //           type='integer'
        //           v-model={row.shippedQuantity}
        //           placeholder={this.$t('请输入')}
        //           min='1'
        //           max={row.planDeliveryQty}
        //           transfer
        //           clearable
        //         />
        //       ]
        //     }
        //   }
        // },
        // {
        //   field: 'baseUnitCode',
        //   title: this.$t('单位'),
        //   formatter: ({ cellValue, row }) => {
        //     return cellValue ? cellValue + '-' + row.baseUnitName : ''
        //   }
        // },
        // {
        //   field: 'categoryCode',
        //   title: this.$t('品类'),
        //   formatter: ({ cellValue, row }) => {
        //     return cellValue ? cellValue + '-' + row.categoryName : ''
        //   }
        // },
        // {
        //   field: 'itemRemark',
        //   title: this.$t('行备注'),
        //   editRender: {},
        //   slots: {
        //     edit: ({ row }) => {
        //       return [
        //         <vxe-input
        //           v-model={row.itemRemark}
        //           placeholder={this.$t('请输入')}
        //           transfer
        //           clearable
        //         />
        //       ]
        //     }
        //   }
        // }
      ]
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (this.editable) {
        btns = [{ code: 'delete', name: this.$t('删除'), status: 'info', loading: false }]
      }
      return btns
    }
  },
  mounted() {
    // if (this.pageType === 'create') {
    //   let data = []
    //   data = sessionStorage.getItem('deliveryPlanSelectedRecords')
    //     ? JSON.parse(sessionStorage.getItem('deliveryPlanSelectedRecords'))
    //     : []
    //   this.tableData = data.map((item) => {
    //     return {
    //       planId: item.id,
    //       deliveryPlanNo: item.deliveryPlanCode,
    //       supplierTenantId: item.supplierTenantId,
    //       supplierCode: item.supplierCode,
    //       supplierName: item.supplierName,
    //       itemCode: item.itemCode,
    //       itemName: item.itemName,
    //       baseUnitCode: item.unitCode,
    //       baseUnitName: item.unit,
    //       categoryCode: item.catoryCode,
    //       categoryName: item.catoryName,
    //       planDeliveryQty: Number(item.planDeliveryQty),
    //       shippedQuantity: Number(item.planDeliveryQty)
    //     }
    //   })
    //   this.$emit('updateDetail', this.tableData)
    // } else {
    //   this.getTableData()
    // }
  },
  methods: {
    // 查看履历表
    handleCheck(row) {
      this.$dialog({
        data: {
          title: this.$t('履历表'),
          id: row.id
        },
        modal: () => import('./ResumeDialog.vue'),
        success: () => {}
      })
    },
    /**
     * 获取表格数据
     */
    async getTableData() {
      try {
        this.loading = true
        let params = {
          ids: [this.$route.query?.id]
        }
        const res = await this.$API.supplierVmiManagement.detailInventoryManagementApi(params)
        if (res.code === 200) {
          this.tableData = res.data
          this.$emit('updateDetail', this.tableData)
        } else {
          this.$toast({ content: res.msg || this.$t('获取数据失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    /**
     * 处理工具栏按钮点击
     */
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      switch (e.code) {
        case 'delete':
          this.handleDelete()
          break
        default:
          break
      }
    },

    /**
     * 处理删除
     */
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    },

    /**
     * 处理编辑完成
     */
    editComplete(args) {
      if (args.$event) {
        // 保存数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sc-table {
  :deep(.vxe-table) {
    .vxe-body--row {
      &.row--editing {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
