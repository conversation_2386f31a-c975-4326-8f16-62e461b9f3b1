<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="dataInfo.sampleItems"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :fix-height="300"
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="false"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>

      <template #moldHistoryDefault="{ row, column }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleCheck(row, column)">
            {{ row[column.field] }}
          </span>
        </div>
      </template>
      <!--   -->
      <template #uploadFile="{ row, column }">
        <div>
          <div class="uipload-box">
            <div id="drop" class="droparea">
              <div class="click-upbox" id="browse">
                <template v-if="dataInfo.status == 2">
                  <span class="file-info">
                    <span class="file-name" @click="handleCheckUpload(row, column)">
                      {{ row[column.field] }}
                    </span>
                    <label class="reupload-label" v-if="row.passFlag == '否'">
                      重新上传
                      <input
                        type="file"
                        class="upload-input"
                        @change="(event) => chooseFiles(event, row)"
                      />
                    </label>
                  </span>
                </template>

                <template v-else-if="dataInfo.status == 1">
                  <template v-if="row[column.field]">
                    <span class="file-info">
                      <span class="file-name" @click="handleCheckUpload(row, column)">
                        {{ row[column.field] }}
                      </span>
                      <label class="reupload-label">
                        重新上传
                        <input
                          type="file"
                          class="upload-input"
                          @change="(event) => chooseFiles(event, row)"
                        />
                      </label>
                    </span>
                  </template>
                  <label v-else class="upload-label">
                    上传
                    <input
                      type="file"
                      class="upload-input"
                      @change="(event) => chooseFiles(event, row)"
                    />
                  </label>
                </template>
              </div>
            </div>
          </div>
        </div>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  name: 'ItemTab',
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => ({
        sampleItems: []
      })
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        shippedQuantity: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        // {
        //   type: 'checkbox',
        //   width: 50,
        //   fixed: 'left',
        //   align: 'center'
        // },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'qualificationItemName',
          title: this.$t('资质项名称')
        },
        {
          field: 'fileName',
          width: 300,
          title: this.$t('供应商上传资质项文件'),
          slots: {
            default: 'uploadFile'
          }
        },
        {
          field: 'remark',
          title: this.$t('备注')
        },
        {
          field: 'docName',
          title: this.$t('资质项附件模板'),
          slots: {
            default: 'moldHistoryDefault'
          }
        },
        {
          field: 'approveTypeName',
          title: this.$t('审批类型')
        },
        {
          field: 'approveUserName',
          title: this.$t('审批人')
        },
        {
          field: 'passFlag',
          title: this.$t('是否通过')
        },
        {
          field: 'rejectReason',
          title: this.$t('驳回原因')
        },
        {
          field: 'updateTime',
          title: this.$t('更新时间')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        }
        // {
        //   field: 'shippedQuantity',
        //   title: this.$t('发货数量'),
        //   editRender: {},
        //   slots: {
        //     edit: ({ row }) => {
        //       return [
        //         <vxe-input
        //           type='integer'
        //           v-model={row.shippedQuantity}
        //           placeholder={this.$t('请输入')}
        //           min='1'
        //           max={row.planDeliveryQty}
        //           transfer
        //           clearable
        //         />
        //       ]
        //     }
        //   }
        // },
        // {
        //   field: 'baseUnitCode',
        //   title: this.$t('单位'),
        //   formatter: ({ cellValue, row }) => {
        //     return cellValue ? cellValue + '-' + row.baseUnitName : ''
        //   }
        // },
      ]
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = [
        // { code: 'delete', name: this.$t('驳回'), status: 'info', loading: false },
        // { code: 'delete1', name: this.$t('保存'), status: 'info', loading: false },
        { code: 'viewDocument', name: this.$t('图纸查询'), status: 'info', loading: false }
      ]
      return btns
    }
  },
  mounted() {
    // if (this.pageType === 'create') {
    //   let data = []
    //   data = sessionStorage.getItem('deliveryPlanSelectedRecords')
    //     ? JSON.parse(sessionStorage.getItem('deliveryPlanSelectedRecords'))
    //     : []
    //   this.tableData = data.map((item) => {
    //     return {
    //       planId: item.id,
    //       deliveryPlanNo: item.deliveryPlanCode,
    //       supplierTenantId: item.supplierTenantId,
    //       supplierCode: item.supplierCode,
    //       supplierName: item.supplierName,
    //       itemCode: item.itemCode,
    //       itemName: item.itemName,
    //       baseUnitCode: item.unitCode,
    //       baseUnitName: item.unit,
    //       categoryCode: item.catoryCode,
    //       categoryName: item.catoryName,
    //       planDeliveryQty: Number(item.planDeliveryQty),
    //       shippedQuantity: Number(item.planDeliveryQty)
    //     }
    //   })
    //   this.$emit('updateDetail', this.tableData)
    // } else {
    //   this.getTableData()
    // }
  },
  methods: {
    chooseFiles(event, row) {
      let _files = event.target.files
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择需要上传的文件.')
        })
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('UploadFiles', _files[i])
        if (_files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$store.commit('startLoading')
      this.$API.fileService.uploadPrivateFile(_data).then((res) => {
        this.$store.commit('endLoading')
        row.fileId = res.data.id
        row.fileName = res.data.fileName
        row.fileUrl = res.data.url
        // this.$emit('confirm-function', res?.data)
      })
    },

    // 下载
    async handleCheck(row) {
      const res = await this.$API.sendSampleCertification.getPlmFileUrl(row.docId)
      if (res.code == 200 && res.data) {
        window.open(res.data)
      }
      // this.$API.fileService
      //   .downloadPublicFile({
      //     id: row.docId
      //   })
      //   .then((res) => {
      //     download({
      //       fileName: row.docName,
      //       blob: res.data
      //     })
      //   })
    },
    async handleCheckUpload(row) {
      const res = await this.$API.sendSampleCertification.getPlmFileUrl(row.fileId)
      if (res.code == 200 && res.data) {
        window.open(res.data)
      }

      // this.$API.fileService
      //   .downloadPublicFile({
      //     id: row.fileId
      //   })
      //   .then((res) => {
      //     download({
      //       fileName: row.fileName,
      //       blob: res.data
      //     })
      //   })
    },
    /**
     * 获取表格数据
     */
    async getTableData() {
      try {
        this.loading = true
        let params = {
          ids: [this.$route.query?.id]
        }
        const res = await this.$API.supplierVmiManagement.detailInventoryManagementApi(params)
        if (res.code === 200) {
          this.tableData = res.data
          this.$emit('updateDetail', this.tableData)
        } else {
          this.$toast({ content: res.msg || this.$t('获取数据失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    /**
     * 处理工具栏按钮点击
     */
    handleClickToolBar(e) {
      if (e.code == 'viewDocument') {
        this.$router.push({
          name: 'drawings-check',
          query: {
            itemCode: this.dataInfo.materialCode,
            timeStamp: new Date().getTime()
          }
        })

        return
      }
      // switch (e.code) {
      //   case 'delete':
      //     this.handleDelete()
      //     break
      //   default:
      //     break
      // }
    },

    /**
     * 处理删除
     */
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    },

    /**
     * 处理编辑完成
     */
    editComplete(args) {
      if (args.$event) {
        // 保存数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sc-table {
  :deep(.vxe-table) {
    .vxe-body--row {
      &.row--editing {
        background-color: #f5f7fa;
      }
    }
  }
}

.upload-label {
  cursor: pointer;
}

.upload-input {
  display: none;
}

.file-info {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  color: #2783fe;
  cursor: pointer;
  display: inline-block;
  width: 210px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.upload-label,
.reupload-label {
  position: relative;
  display: inline-block;
  color: #2783fe;
  cursor: pointer;
  font-size: 13px;
}

.upload-input {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}
</style>
