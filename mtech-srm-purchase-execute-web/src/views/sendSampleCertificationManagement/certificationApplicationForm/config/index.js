import { i18n } from '@/main.js'

// 状态
export const statusOptions = [
  { value: 1, text: i18n.t('已发布') },
  { value: 2, text: i18n.t('待审阅') },
  { value: 3, text: i18n.t('已驳回') },
  { value: 4, text: i18n.t('已完成') }
]

// 按钮
export const toolbar = [
  {
    code: 'approve',
    name: i18n.t('确认'),
    status: 'success',
    loading: false
  },
  {
    code: 'reject',
    name: i18n.t('驳回'),
    status: 'danger',
    loading: false
  }
]

export const columnData = [
  {
    width: 50,
    type: 'checkbox',
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'company',
    title: i18n.t('公司'),
    minWidth: 120,
    formatter: ({ row }) => {
      if (row.company && row.companyCode) {
        return `${row.companyCode}-${row.company}`
      }
      return row.company || ''
    }
  },
  {
    field: 'productLine',
    title: i18n.t('产品线'),
    minWidth: 100
  },
  {
    field: 'authApplyNo',
    title: i18n.t('认证申请单号'),
    minWidth: 140,
    slots: { default: 'authApplyNo' }
  },
  {
    field: 'authName',
    title: i18n.t('名称'),
    minWidth: 120
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称')
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称'),
    minWidth: 150
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商代码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 150
  },
  {
    field: 'statusName',
    title: i18n.t('状态'),
    minWidth: 80
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'categoryName',
    title: i18n.t('品类'),
    minWidth: 100
  }
]

// 详情页面收集清单表格配置
export const collectionListColumns = [
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 60,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'materialName',
    title: i18n.t('名称'),
    minWidth: 150
  },
  {
    field: 'secondaryMaterial',
    title: i18n.t('二级物料'),
    minWidth: 120
  },
  {
    field: 'authRegion',
    title: i18n.t('认证地区'),
    minWidth: 100
  },
  {
    field: 'authClassType',
    title: i18n.t('认证类型'),
    minWidth: 100
  },
  {
    field: 'makerCn',
    title: i18n.t('制造商/商标(中文)'),
    minWidth: 150
  },
  {
    field: 'makerEn',
    title: i18n.t('制造商/商标(英文)'),
    minWidth: 150
  },
  {
    field: 'factoryName',
    title: i18n.t('工厂名称'),
    minWidth: 120
  },
  {
    field: 'model',
    title: i18n.t('型号'),
    minWidth: 100
  },
  {
    field: 'technicalParam',
    title: i18n.t('技术参数'),
    minWidth: 120
  },
  {
    field: 'standard',
    title: i18n.t('标准'),
    minWidth: 100
  },
  {
    field: 'certificateNo',
    title: i18n.t('证书编码'),
    minWidth: 120
  },
  {
    field: 'fileName',
    title: i18n.t('文档名称'),
    minWidth: 120,
    slots: {
      default: 'fileName',
      edit: 'fileNameEdit'
    }
  },
  {
    field: 'effectiveTime',
    title: i18n.t('生效日期'),
    minWidth: 100
  },
  {
    field: 'failureTime',
    title: i18n.t('失效日期'),
    minWidth: 100
  }
]
