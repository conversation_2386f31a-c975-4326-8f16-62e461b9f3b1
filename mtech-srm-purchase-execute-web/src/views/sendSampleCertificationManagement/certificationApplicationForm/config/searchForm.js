/*
 * @Author: wb_qianxiaoxia <EMAIL>
 * @Date: 2025-06-19 17:19:31
 * @LastEditors: wb_qianxiaoxia <EMAIL>
 * @LastEditTime: 2025-06-19 18:49:31
 * @FilePath: /mtech-srm-purchase-execute-web/src/views/sendSampleCertificationManagement/certificationApplicationForm/config/searchForm.js
 * @Description:
 */
/**
 * 认证申请单搜索表单配置
 */
import { i18n } from '@/main.js'
import { statusOptions } from './index'

export const getSearchFormItems = () => [
  {
    label: i18n.t('认证申请单号'),
    prop: 'authApplyNo',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('名称'),
    prop: 'authName',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('公司'),
    prop: 'company',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('物料编码'),
    prop: 'materialCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('供应商'),
    prop: 'supplierCode',
    component: 'RemoteAutocomplete',
    props: {
      url: '/masterDataManagement/tenant/supplier/paged-query',
      multiple: false,
      placeholder: i18n.t('请选择供应商'),
      fields: { text: 'supplierName', value: 'supplierCode' },
      searchFields: ['supplierName', 'supplierCode']
    }
  },
  {
    label: i18n.t('创建者'),
    prop: 'createdBy',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('创建时间'),
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('状态'),
    prop: 'status',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      dataSource: statusOptions
    }
  }
]
