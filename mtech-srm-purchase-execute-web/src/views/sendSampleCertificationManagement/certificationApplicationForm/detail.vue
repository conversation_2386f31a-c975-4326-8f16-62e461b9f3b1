<!--
 * @Author: wb_qianxiaoxia <EMAIL>
 * @Date: 2025-06-18 14:41:43
 * @LastEditors: wb_qianxiaoxia <EMAIL>
 * @LastEditTime: 2025-07-08 17:26:30
 * @FilePath: /mtech-srm-purchase-execute-web/src/views/sendSampleCertificationManagement/certificationApplicationForm/detail.vue
 * @Description: 采方-认证申请单-详情
-->
<template>
  <div class="full-height pt20 vertical-flex-box detail-fix-wrap">
    <div class="detail-header-box">
      <div class="middle-blank"></div>
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回>') }}</mt-button>
    </div>
    <div class="bottom-tables">
      <mt-tabs :e-tab="false" :data-source="tabList" @handleSelectTab="handleSelectTab"></mt-tabs>
      <div class="flex-fit bottom-box">
        <!-- 详细信息 -->
        <div v-show="currentTabIndex === 0" class="detail-content">
          <!-- 基本属性 -->
          <!-- 基本属性 -->
          <div class="info-section">
            <div class="section-title">{{ $t('基本属性') }}</div>
            <div class="section-content">
              <div class="form-layout">
                <div class="form-item">
                  <div class="form-label">{{ $t('认证申请单号') }}</div>
                  <div class="form-value">{{ formData.authApplyNo || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('名称') }}</div>
                  <div class="form-value">{{ formData.name || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('送样申请单编码') }}</div>
                  <div class="form-value">{{ formData.sampleApplyNo || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('零部件认证要求') }}</div>
                  <div class="form-value">{{ formData.authRequirement || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('创建者') }}</div>
                  <div class="form-value">{{ formData.createUserName || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('创建时间') }}</div>
                  <div class="form-value">{{ formData.createTime || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('驳回原因') }}</div>
                  <div class="form-value">{{ formData.rejectReason || '-' }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 收集清单 -->
          <div class="info-section">
            <div class="section-title">{{ $t('收集清单') }}</div>
            <div class="section-content">
              <div class="collection-list">
                <sc-table
                  ref="collectionTableRef"
                  row-id="id"
                  grid-id="58702eaa-cd73-4aaa-bc55-fde1b61b7af8"
                  show-overflow
                  keep-source
                  :loading="collectionLoading"
                  :columns="collectionColumns"
                  :table-data="collectionData"
                  :is-show-refresh-bth="true"
                  @refresh="getDetailData"
                >
                  <template #custom-tools>
                    <vxe-button
                      v-for="item in toolbar"
                      :key="item.code"
                      :status="item.status"
                      size="small"
                      :disabled="getButtonDisabled(item.code)"
                      @click="handleCollectionToolbar(item)"
                    >
                      {{ item.name }}
                    </vxe-button>
                  </template>

                  <template #fileName="{ row }">
                    <span
                      v-if="row.fileId && row.fileName"
                      class="file-download-link"
                      @click="handleDownload(row)"
                    >
                      {{ row.fileName }}
                    </span>
                    <span v-else>{{ row.fileName || '-' }}</span>
                  </template>
                </sc-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 驳回原因弹窗 -->
    <RejectDialog
      v-if="showRejectDialog"
      @confirm-function="handleConfirmReject"
      @cancel-function="hideRejectDialog"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import RejectDialog from './components/RejectDialog.vue'
import { collectionListColumns, toolbar } from './config/index'
import { download } from '@/utils/utils'

export default {
  name: 'CertificationApplicationFormSupDetail',
  components: { ScTable, RejectDialog },
  data() {
    return {
      toolbar: toolbar,
      currentTabIndex: 0,
      collectionLoading: false,
      formData: {},
      collectionData: [],
      tabList: [{ title: this.$t('详细信息') }],
      collectionColumns: this.getCollectionColumns(),
      showRejectDialog: false
    }
  },

  computed: {
    detailId() {
      return this.$route.query?.id
    },
    // 是否可确认（状态为待审核）
    canApprove() {
      return this.formData.status === '2' // 2表示待审核状态
    },
    // 是否可驳回（状态为待审核）
    canReject() {
      return this.formData.status === '2' // 2表示待审核状态
    }
  },

  created() {
    this.getDetailData()
  },

  methods: {
    /**
     * 校验创建者权限
     * @param {Object} record - 需要校验的记录
     * @returns {boolean} 是否通过校验
     */
    validateCreatorPermission(record) {
      try {
        const currentUser = JSON.parse(sessionStorage.getItem('userInfo'))
        if (!currentUser || !currentUser.uid) {
          this.$toast({ content: this.$t('无法获取当前用户信息'), type: 'error' })
          return false
        }

        // 检查记录是否是当前用户创建的
        if (record.createUserId !== currentUser.uid) {
          this.$toast({ content: this.$t('只能操作当前登录人创建的数据'), type: 'warning' })
          return false
        }

        return true
      } catch (error) {
        console.error('权限校验错误:', error)
        this.$toast({ content: this.$t('权限校验失败'), type: 'error' })
        return false
      }
    },

    /**
     * 获取表格列配置
     */
    getCollectionColumns() {
      return collectionListColumns.map((column) => {
        // 保留fileName字段的default slots配置，移除编辑相关配置
        if (column.field === 'fileName') {
          return {
            ...column,
            editRender: undefined,
            slots: column.slots ? { default: column.slots.default } : undefined
          }
        }
        return {
          ...column,
          // 移除所有编辑相关配置
          editRender: undefined,
          slots: undefined
        }
      })
    },

    /**
     * 获取详情数据
     */
    async getDetailData() {
      this.collectionLoading = true
      try {
        const res =
          await this.$API.sendSampleCertification.getCertificationApplicationFormSupDetailApi(
            this.detailId
          )

        if (res.code === 200 && res.data) {
          // 填充基本信息
          this.formData = {
            authApplyNo: res.data.authApplyNo || '',
            code: res.data.authApplyNo || '',
            name: res.data.authName || '',
            sampleApplyNo: res.data.sampleApplyNo || '',
            authRequirement: res.data.authRequirement || '',
            createUserName: res.data.createdBy || '',
            createTime: res.data.createTime || '',
            rejectReason: res.data.rejectReason || '',
            status: res.data.status || '1',
            createUserId: res.data.createUserId || '' // 保留用于权限校验
          }

          // 填充收集清单数据
          if (res.data.detailList && Array.isArray(res.data.detailList)) {
            this.collectionData = res.data.detailList.map((item) => ({
              id: item.id,
              materialCode: item.materialCode || '',
              materialName: item.materialName || '',
              secondaryMaterial: item.secondaryMaterial || '',
              authRegion: item.authRegion || '',
              authClassType: item.authClassType || '',
              makerCn: item.makerCn || '',
              makerEn: item.makerEn || '',
              factoryName: item.factoryName || '',
              model: item.model || '',
              technicalParam: item.technicalParam || '',
              standard: item.standard || '',
              certificateNo: item.certificateNo || '',
              fileName: item.fileName || '',
              fileId: item.fileId || '',
              effectiveTime: item.effectiveTime || null,
              failureTime: item.failureTime || null,
              authFlag: item.authFlag || '4'
            }))

            console.log('this.collectionData----->', this.collectionData)
          } else {
            this.collectionData = []
          }
        }
      } catch (error) {
        console.log(error)
        this.$toast({ content: this.$t('获取详情数据失败'), type: 'error' })
      } finally {
        this.collectionLoading = false
      }
    },

    /**
     * 标签切换
     * @param {number} index - 标签索引
     */
    handleSelectTab(index) {
      this.currentTabIndex = index
    },

    /**
     * 获取按钮禁用状态
     * @param {string} code - 按钮代码
     * @returns {boolean} 是否禁用
     */
    getButtonDisabled(code) {
      if (code === 'approve') {
        return !this.canApprove
      }
      if (code === 'reject') {
        return !this.canReject
      }
      return false
    },

    /**
     * 收集清单工具栏操作
     * @param {Object} item - 按钮配置
     */
    handleCollectionToolbar(item) {
      // 防抖处理，避免重复点击导致的问题
      if (this.isProcessing) {
        return
      }

      try {
        this.isProcessing = true

        switch (item.code) {
          case 'approve':
            this.handleConfirmApprove()
            break
          case 'reject':
            this.handleRejectSelected()
            break
          default:
            console.log('未知操作:', item.code)
        }
      } catch (error) {
        console.error('工具栏操作错误:', error)
        this.$toast({
          content: this.$t('操作失败，请稍后重试'),
          type: 'error'
        })
      } finally {
        // 使用setTimeout确保操作完成后再重置状态
        setTimeout(() => {
          this.isProcessing = false
        }, 500)
      }
    },

    /**
     * 确认处理
     */
    async handleConfirmApprove() {
      if (!this.canApprove) {
        this.$toast({ content: this.$t('当前状态不允许确认'), type: 'warning' })
        return
      }

      // 校验创建者权限
      if (!this.validateCreatorPermission(this.formData)) {
        return
      }

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认通过此认证申请单？'),
          type: 'warning'
        },
        success: async () => {
          try {
            const res =
              await this.$API.sendSampleCertification.approveCertificationApplicationFormApi({
                ids: [this.detailId],
                operateType: 0 // 0表示确认/通过
              })

            if (res.code === 200) {
              this.$toast({ content: this.$t('确认成功'), type: 'success' })
              this.goBack()
            } else {
              this.$toast({ content: res.message || this.$t('确认失败'), type: 'error' })
            }
          } catch (error) {
            console.error('确认认证申请单失败:', error)
            this.$toast({ content: error.msg || this.$t('操作异常'), type: 'error' })
          }
        }
      })
    },

    /**
     * 处理驳回操作
     */
    handleRejectSelected() {
      if (!this.canReject) {
        this.$toast({ content: this.$t('当前状态不允许驳回'), type: 'warning' })
        return
      }

      // 校验创建者权限
      if (!this.validateCreatorPermission(this.formData)) {
        return
      }

      this.showRejectDialog = true
    },

    /**
     * 隐藏驳回弹窗
     */
    hideRejectDialog() {
      this.showRejectDialog = false
    },

    /**
     * 确认驳回
     */
    async handleConfirmReject(rejectReason) {
      try {
        const res = await this.$API.sendSampleCertification.approveCertificationApplicationFormApi({
          ids: [this.detailId],
          operateType: 1, // 1表示驳回
          rejectReason: rejectReason
        })

        if (res.code === 200) {
          this.$toast({ content: this.$t('驳回成功'), type: 'success' })
          this.hideRejectDialog()
          this.goBack()
        } else {
          this.$toast({ content: res.message || this.$t('驳回失败'), type: 'error' })
        }
      } catch (error) {
        console.error('驳回认证申请单失败:', error)
        this.$toast({ content: this.$t('驳回失败'), type: 'error' })
      }
    },

    /**
     * 下载文件
     * @param {Object} row - 文件行数据
     */
    handleDownload(row) {
      if (!row.fileId) {
        this.$toast({ content: this.$t('文件不存在'), type: 'warning' })
        return
      }

      this.$loading()
      this.$API.fileService
        .downloadPrivateFile({ id: row.fileId })
        .then((res) => {
          this.$hloading()
          download({
            fileName: row.fileName,
            blob: new Blob([res.data])
          })
        })
        .catch((error) => {
          this.$hloading()
          console.error('文件下载失败:', error)
          this.$toast({ content: this.$t('文件下载失败'), type: 'error' })
        })
    },

    /**
     * 返回
     */
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-fix-wrap {
  padding: 20px;

  .detail-header-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 10px;
    min-height: 32px;

    .middle-blank {
      flex: 1;
    }

    .mt-button {
      margin-left: 10px;
    }
  }

  .bottom-tables {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .bottom-box {
      flex: 1;
      overflow: auto;
    }
  }

  .detail-content {
    width: 100%;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    padding: 20px;
    .form-layout {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px 16px;
      align-items: start;

      .form-item {
        display: flex;
        flex-direction: row;
        align-items: center;

        &.form-item-wide {
          grid-column: span 2;
        }
      }

      .form-label {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        width: 120px;
        flex-shrink: 0;
        margin-right: 12px;
        margin-bottom: 0;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.5;
      }

      .form-value {
        flex: 1;
        min-height: 32px;
        line-height: 1.6;
        font-size: 14px;
        color: #232b39;
        padding: 8px 12px;
        word-break: break-all;
        display: flex;
        align-items: center;

        &.reject-reason {
          background: #fff2f0;
          border-color: #ffccc7;
          color: #ff4d4f;
          font-weight: 500;
        }
      }
    }

    .info-section {
      margin-bottom: 20px;

      .section-title {
        padding: 12px 16px;
        background: #f5f5f5;
        border: 1px solid #e8e8e8;
        border-radius: 4px 4px 0 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-text {
          flex-shrink: 0;
        }

        .title-tip {
          font-size: 14px;
          font-weight: 600;
          color: #ff4d4f;
          margin-left: 16px;
          line-height: 1.4;
        }
      }

      .section-content {
        border: 1px solid #e8e8e8;
        border-top: none;
        border-radius: 0 0 4px 4px;
        padding: 20px;
        background: #fff;
      }
    }
  }

  .detail-info-grid {
    .detail-field {
      flex: 1;
      min-width: 0;
      display: flex;
      align-items: center;

      .field-key {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        width: 80px;
        flex-shrink: 0;
        margin-right: 8px;
        text-align: start;
      }

      .field-value {
        flex: 1;
        height: 34px;
        line-height: 34px;
        font-size: 14px;
        color: #232b39;
        padding: 0 8px;
        word-break: break-all;
      }
    }
  }

  .collection-list {
    margin-top: 0;
  }
}

/deep/ .mt-tabs {
  .mt-tabs-container {
    width: 100%;
  }
}

// 下载链接样式
.file-download-link {
  color: #007acc;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: #40a9ff;
    text-decoration: none;
  }
}
</style>
