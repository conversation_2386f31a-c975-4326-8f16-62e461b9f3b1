<!--
 * @Author: wb_qianxiaoxia <EMAIL>
 * @Date: 2025-06-19 19:09:44
 * @LastEditors: wb_qianxiaoxia <EMAIL>
 * @LastEditTime: 2025-06-19 19:39:14
 * @FilePath: /mtech-srm-purchase-execute-web/src/views/sendSampleCertificationManagement/certificationApplicationForm/components/RejectDialog.vue
 * @Description: 
-->
<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="rejectReason" :label="$t('驳回原因')">
          <vxe-textarea
            v-model="formObject.rejectReason"
            :placeholder="$t('请输入驳回原因')"
            maxlength="200"
            show-word-count
            style="margin-top: 20px"
            :autosize="{ minRows: 3, maxRows: 5 }"
          ></vxe-textarea>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { Textarea as VxeTextarea } from 'vxe-table'
export default {
  components: { VxeTextarea },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认驳回') }
        }
      ],
      formObject: {
        rejectReason: ''
      },
      formRules: {
        rejectReason: [{ required: true, message: this.$t('请输入驳回原因'), trigger: 'blur' }]
      }
    }
  },
  computed: {
    header() {
      return this.$t('驳回原因')
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formObject.rejectReason)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0;
}
</style>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    .dialog-content {
      .mt-form {
        textarea.e-input,
        .e-input-group textarea.e-input,
        .e-input-group.e-control-wrapper textarea.e-input {
          height: auto !important;
        }
      }
    }
  }
}
</style>
