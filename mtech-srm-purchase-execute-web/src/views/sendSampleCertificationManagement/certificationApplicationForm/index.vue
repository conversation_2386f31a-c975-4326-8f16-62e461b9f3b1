<!--
 * @Author: wb_qianxiaoxia <EMAIL>
 * @Date: 2025-06-17 21:55:50
 * @LastEditors: wb_qianxiaoxia <EMAIL>
 * @LastEditTime: 2025-07-07 15:15:13
 * @FilePath: /mtech-srm-purchase-execute-web/src/views/sendSampleCertificationManagement/certificationApplicationForm/index.vue
 * @Description: 采方-认证申请单
-->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      gird-id="bc5101eb-e5d5-4235-8f21-8150f63ef072"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #authApplyNo="{ row }">
        <span class="link-text" @click="handleViewDetail(row)">{{ row.authApplyNo }}</span>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <!-- 驳回原因弹窗 -->
    <RejectDialog
      v-if="showRejectDialog"
      @confirm-function="handleConfirmReject"
      @cancel-function="hideRejectDialog"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import RejectDialog from './components/RejectDialog.vue'
import { columnData, toolbar } from './config/index'
import { getSearchFormItems } from './config/searchForm'
import dayjs from 'dayjs'

export default {
  name: 'CertificationApplicationFormSup',
  components: { CollapseSearch, ScTable, RemoteAutocomplete, RejectDialog },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      columns: columnData,
      toolbar: toolbar,
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      selectedRecords: [],
      showRejectDialog: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems()
      // 为创建时间字段添加onChange事件处理
      const dateFields = ['createTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },

  created() {
    this.getTableData()
  },

  methods: {
    /**
     * 校验创建者权限
     * @param {Array} records - 需要校验的记录数组
     * @returns {boolean} 是否通过校验
     */
    validateCreatorPermission(records) {
      try {
        const currentUser = JSON.parse(sessionStorage.getItem('userInfo'))
        if (!currentUser || !currentUser.uid) {
          this.$toast({ content: this.$t('无法获取当前用户信息'), type: 'error' })
          return false
        }

        // 检查所有记录是否都是当前用户创建的
        const invalidRecords = records.filter((record) => record.createUserId !== currentUser.uid)

        if (invalidRecords.length > 0) {
          this.$toast({ content: this.$t('只能操作当前登录人创建的数据'), type: 'warning' })
          return false
        }

        return true
      } catch (error) {
        console.error('权限校验错误:', error)
        this.$toast({ content: this.$t('权限校验失败'), type: 'error' })
        return false
      }
    },

    /**
     * 处理工具栏按钮点击事件
     * @param {Object} item - 按钮配置
     */
    handleClickToolBar(item) {
      switch (item.code) {
        case 'reject':
          this.handleRejectSelected()
          break
        case 'approve':
          this.handleApproveSelected()
          break
        default:
          console.log('未知操作:', item.code)
      }
    },

    /**
     * 处理选中数据驳回
     */
    handleRejectSelected() {
      // 获取选中的记录
      const selectedRecords = this.tableRef?.getCheckboxRecords() || []

      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一条数据'), type: 'warning' })
        return
      }

      // 校验创建者权限
      if (!this.validateCreatorPermission(selectedRecords)) {
        return
      }

      // 调试：打印选中数据的状态信息
      console.log(
        '选中的记录:',
        selectedRecords.map((record) => ({
          id: record.id,
          status: record.status,
          statusName: record.statusName,
          statusType: typeof record.status
        }))
      )

      // 过滤出待审阅状态的数据（status为2或"2"表示待审阅）
      const pendingReviewRecords = selectedRecords.filter(
        (record) => record.status == 2 || record.status === '2'
      )
      const nonPendingReviewRecords = selectedRecords.filter(
        (record) => record.status != 2 && record.status !== '2'
      )

      // 检查是否有符合条件的数据
      if (pendingReviewRecords.length === 0) {
        this.$toast({
          content: this.$t('选中的数据中没有待审阅状态的记录，仅待审阅状态的数据可以驳回'),
          type: 'warning'
        })
        return
      }

      // 如果混合选择了不同状态的数据，给出提示但允许操作待审阅的数据
      if (nonPendingReviewRecords.length > 0) {
        const statusNames = nonPendingReviewRecords
          .map((record) => record.statusName || this.$t('未知状态'))
          .join('、')
        this.$toast({
          content: this.$t(
            `已过滤出${pendingReviewRecords.length}条待审阅状态的数据进行驳回，跳过${nonPendingReviewRecords.length}条非待审阅状态的数据（${statusNames}）`
          ),
          type: 'info'
        })
      }

      // 更新选中记录为符合条件的记录
      this.selectedRecords = pendingReviewRecords
      this.showRejectDialog = true
    },

    /**
     * 隐藏驳回弹窗
     */
    hideRejectDialog() {
      this.showRejectDialog = false
    },

    /**
     * 确认驳回
     */
    async handleConfirmReject(rejectReason) {
      // 提取选中记录的 ID
      const selectedIds = this.selectedRecords.map((record) => record.id)

      try {
        const res = await this.$API.sendSampleCertification.approveCertificationApplicationFormApi({
          ids: selectedIds,
          operateType: 1, // 1表示驳回
          rejectReason: rejectReason
        })

        if (res.code === 200) {
          this.$toast({ content: this.$t('驳回成功'), type: 'success' })
          this.hideRejectDialog()
          // 清除选中状态
          this.tableRef?.clearCheckboxRow()
          // 刷新表格数据
          this.handleSearch()
        } else {
          this.$toast({ content: res.msg || this.$t('驳回失败'), type: 'error' })
        }
      } catch (error) {
        console.error('批量驳回认证申请单失败:', error)
        this.$toast({ content: this.$t('驳回失败'), type: 'error' })
      }
    },

    /**
     * 处理选中数据确认
     */
    async handleApproveSelected() {
      // 获取选中的记录
      const selectedRecords = this.tableRef?.getCheckboxRecords() || []

      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一条数据'), type: 'warning' })
        return
      }

      // 校验创建者权限
      if (!this.validateCreatorPermission(selectedRecords)) {
        return
      }

      // 提取选中记录的 ID
      const selectedIds = selectedRecords.map((record) => record.id)

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认通过选中的 ${selectedRecords.length} 条认证申请单？`),
          type: 'warning'
        },
        success: async () => {
          try {
            const res =
              await this.$API.sendSampleCertification.approveCertificationApplicationFormApi({
                ids: selectedIds,
                operateType: 0 // 0表示确认/通过
              })

            if (res.code === 200) {
              this.$toast({ content: this.$t('确认成功'), type: 'success' })
              // 清除选中状态
              this.tableRef?.clearCheckboxRow()
              // 刷新表格数据
              this.handleSearch()
            } else {
              this.$toast({ content: res.msg || this.$t('确认失败'), type: 'error' })
            }
          } catch (error) {
            console.error('批量确认认证申请单失败:', error)
            this.$toast({ content: this.$t('确认失败'), type: 'error' })
          }
        }
      })
    },

    /**
     * 处理日期时间字段change事件
     * @param {Array|Object} dateRange - 日期范围数组或对象
     * @param {string} field - 字段名
     */
    handleDateTimeChange(dateRange, field) {
      // 处理不同格式的日期范围输入
      if (dateRange && Array.isArray(dateRange) && dateRange.length === 2) {
        // 数组格式的日期范围
        this.searchFormModel[`${field}S`] = dayjs(dateRange[0]).valueOf()
        this.searchFormModel[`${field}E`] = dayjs(dateRange[1]).valueOf()
      } else if (dateRange && dateRange.startDate) {
        // 对象格式的日期范围，转换为时间戳
        this.searchFormModel[`${field}S`] = dayjs(dateRange.startDate).startOf('day').valueOf()
        this.searchFormModel[`${field}E`] = dayjs(dateRange.endDate).endOf('day').valueOf()
      } else {
        // 清空日期范围
        this.searchFormModel[`${field}S`] = undefined
        this.searchFormModel[`${field}E`] = undefined
      }
    },

    /**
     * 获取表格数据
     */
    async getTableData() {
      this.loading = true
      try {
        const params = {
          ...this.searchFormModel,
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          }
        }

        // 移除不需要传给后端的字段
        delete params.createTime

        const res =
          await this.$API.sendSampleCertification.pageQueryCertificationApplicationFormApi(params)
        if (res.code === 200) {
          this.tableData = res.data?.records || []
          this.pageSettings.totalPages = res.data?.pages ? +res.data.pages : 0
          this.pageSettings.totalRecordsCount = res.data?.total ? +res.data.total : 0
        } else {
          this.$toast({ content: res.msg || this.$t('查询失败'), type: 'error' })
        }
      } catch (error) {
        console.error('查询认证申请单失败:', error)
        this.$toast({ content: this.$t('查询失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    /**
     * 重置
     */
    handleReset() {
      this.searchFormModel = {}
      this.$refs.searchFormRef?.resetFields()
      this.currentPage = 1
      this.getTableData()
    },

    /**
     * 页码变化
     * @param {number} page - 当前页码
     */
    handleCurrentChange(page) {
      this.currentPage = page
      this.getTableData()
    },

    /**
     * 页面大小变化
     * @param {number} size - 页面大小
     */
    handleSizeChange(size) {
      this.pageSettings.pageSize = size
      this.currentPage = 1
      this.getTableData()
    },

    handleViewDetail(row) {
      this.$router.push({
        path: '/purchase-execute/certification-management-detail',
        query: { id: row.id }
      })
    }
  }
}
</script>

<style scoped>
.toggle-container {
  margin-bottom: 16px;
}
.link-text {
  color: #1890ff;
  cursor: pointer;
  text-decoration: underline;
}
</style>
