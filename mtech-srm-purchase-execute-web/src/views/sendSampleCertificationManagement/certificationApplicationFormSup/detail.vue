<!--
 * @Author: wb_qianxiaoxia <EMAIL>
 * @Date: 2025-06-18 14:41:43
 * @LastEditors: wb_qianxiaoxia <EMAIL>
 * @LastEditTime: 2025-07-23 15:02:15
 * @FilePath: /mtech-srm-purchase-execute-web/src/views/sendSampleCertificationManagement/certificationApplicationFormSup/detail.vue
 * @Description: 供方-认证申请单-详情
-->
<template>
  <div class="full-height pt20 vertical-flex-box detail-fix-wrap">
    <div class="detail-header-box">
      <div class="middle-blank"></div>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        :disabled="!canEdit || formData.status === '4'"
        @click="handleSubmit"
        >{{ $t('提交') }}</mt-button
      >
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回>') }}</mt-button>
    </div>
    <div class="bottom-tables">
      <mt-tabs :e-tab="false" :data-source="tabList" @handleSelectTab="handleSelectTab"></mt-tabs>
      <div class="flex-fit bottom-box">
        <!-- 详细信息 -->
        <div v-show="currentTabIndex === 0" class="detail-content">
          <!-- 基本属性 -->
          <div class="info-section">
            <div class="section-title">{{ $t('基本属性') }}</div>
            <div class="section-content">
              <div class="form-layout">
                <div class="form-item">
                  <div class="form-label">{{ $t('认证申请单号') }}</div>
                  <div class="form-value">{{ formData.authApplyNo || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('名称') }}</div>
                  <div class="form-value">{{ formData.name || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('送样申请单编码') }}</div>
                  <div class="form-value">{{ formData.sampleApplyNo || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('零部件认证要求') }}</div>
                  <div class="form-value">{{ formData.authRequirement || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('创建者') }}</div>
                  <div class="form-value">{{ formData.createUserName || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('创建时间') }}</div>
                  <div class="form-value">{{ formData.createTime || '-' }}</div>
                </div>
                <div class="form-item">
                  <div class="form-label">{{ $t('驳回原因') }}</div>
                  <div class="form-value">{{ formData.rejectReason || '-' }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 收集清单 -->
          <div class="info-section">
            <div class="section-title">
              <span class="title-text">{{ $t('收集清单') }}</span>
              <span class="title-tip"
                >根据零部件认证要求，每组认证要求必填其中之一，若有额外其它认证证书也可进行新增补充</span
              >
            </div>
            <div class="section-content">
              <div class="collection-list">
                <sc-table
                  ref="collectionTableRef"
                  row-id="row-id"
                  grid-id="58702eaa-cd73-4aaa-bc55-fde1b61b7af8"
                  show-overflow
                  keep-source
                  edit-trigger="click"
                  :edit-config="{
                    trigger: 'click',
                    mode: 'row',
                    showStatus: true,
                    activeMethod: this.activeRowMethod
                  }"
                  :loading="collectionLoading"
                  :columns="collectionColumns"
                  :table-data="collectionData"
                  :is-show-refresh-bth="true"
                  @edit-actived="editBegin"
                  @edit-closed="editComplete"
                  @edit-disabled="editDisabledEvent"
                  @refresh="getDetailData"
                >
                  <template #custom-tools>
                    <vxe-button
                      v-for="item in collectionToolbar"
                      :key="item.code"
                      v-bind="item"
                      size="small"
                      :disabled="getButtonDisabled(item.code)"
                      @click="handleCollectionToolbar(item)"
                    >
                      {{ item.name }}
                    </vxe-button>
                  </template>

                  <!-- 编辑模板slots -->
                  <template #secondaryMaterial="{ row }">
                    <span>{{ row.secondMaterial || '-' }}</span>
                  </template>

                  <template #materialCodeEdit="{ row }">
                    <vxe-input
                      v-model="row.materialCode"
                      :placeholder="$t('请输入物料编码')"
                      :disabled="getFieldDisabled(row, 'materialCode')"
                      transfer
                      clearable
                      @focus="handleMaterialCodeFocus(row)"
                      @input="handleMaterialCodeChange(row)"
                    />
                  </template>

                  <template #materialNameEdit="{ row }">
                    <vxe-input
                      v-model="row.materialName"
                      :placeholder="$t('请输入名称')"
                      :disabled="getFieldDisabled(row, 'materialName')"
                      transfer
                      clearable
                      @focus="handleMaterialNameFocus(row)"
                      @input="handleMaterialNameChange(row)"
                    />
                  </template>

                  <template #secondaryMaterialEdit="{ row }">
                    <vxe-select
                      v-model="row.secondMaterial"
                      :placeholder="
                        row.loadingSecondaryMaterial ? $t('加载中...') : $t('请选择二级物料')
                      "
                      :options="row.secondaryMaterialOptions || []"
                      :disabled="
                        getFieldDisabled(row, 'secondMaterial') || row.loadingSecondaryMaterial
                      "
                      transfer
                      :show-clear-button="true"
                      :loading="row.loadingSecondaryMaterial"
                      @focus="handleSecondaryMaterialFocus(row)"
                      @change="handleSecondaryMaterialChange(row)"
                    />
                  </template>

                  <template #authRegionEdit="{ row }">
                    <vxe-input
                      v-model="row.authRegion"
                      :placeholder="$t('请输入认证地区')"
                      :disabled="getFieldDisabled(row, 'authRegion')"
                      transfer
                      clearable
                    />
                  </template>

                  <template #authClassTypeEdit="{ row }">
                    <vxe-select
                      v-model="row.authClassType"
                      :placeholder="$t('请选择认证类型')"
                      :options="certificationTypeOptions"
                      :disabled="getFieldDisabled(row, 'authClassType')"
                      transfer
                      clearable
                      @change="handleCertificationTypeChange(row)"
                    />
                  </template>

                  <template #makerCnEdit="{ row }">
                    <vxe-input
                      v-model="row.makerCn"
                      :placeholder="$t('请输入制造商(中文)')"
                      :class="{ 'field-error': hasFieldError(row, 'makerCn') }"
                      :style="getFieldErrorStyle(row, 'makerCn')"
                      transfer
                      clearable
                      @input="handleFieldChange(row, 'makerCn')"
                    />
                  </template>

                  <template #makerEnEdit="{ row }">
                    <vxe-input
                      v-model="row.makerEn"
                      :placeholder="$t('请输入制造商(英文)')"
                      :class="{ 'field-error': hasFieldError(row, 'makerEn') }"
                      :style="getFieldErrorStyle(row, 'makerEn')"
                      transfer
                      clearable
                      @input="handleFieldChange(row, 'makerEn')"
                    />
                  </template>

                  <template #factoryNameEdit="{ row }">
                    <vxe-input
                      v-model="row.factoryName"
                      :placeholder="$t('请输入工厂名称')"
                      :class="{ 'field-error': hasFieldError(row, 'factoryName') }"
                      :style="getFieldErrorStyle(row, 'factoryName')"
                      transfer
                      clearable
                      @input="handleFieldChange(row, 'factoryName')"
                    />
                  </template>

                  <template #modelEdit="{ row }">
                    <vxe-input
                      v-model="row.model"
                      :placeholder="$t('请输入型号')"
                      :class="{ 'field-error': hasFieldError(row, 'model') }"
                      :style="getFieldErrorStyle(row, 'model')"
                      transfer
                      clearable
                      @input="handleFieldChange(row, 'model')"
                    />
                  </template>

                  <template #technicalParamEdit="{ row }">
                    <vxe-input
                      v-model="row.technicalParam"
                      :placeholder="$t('请输入技术参数')"
                      :class="{ 'field-error': hasFieldError(row, 'technicalParam') }"
                      :style="getFieldErrorStyle(row, 'technicalParam')"
                      transfer
                      clearable
                      @input="handleFieldChange(row, 'technicalParam')"
                    />
                  </template>

                  <template #standardEdit="{ row }">
                    <vxe-input
                      v-model="row.standard"
                      :placeholder="$t('请输入标准')"
                      :class="{ 'field-error': hasFieldError(row, 'standard') }"
                      :style="getFieldErrorStyle(row, 'standard')"
                      transfer
                      clearable
                      @input="handleFieldChange(row, 'standard')"
                    />
                  </template>

                  <template #certificateNoEdit="{ row }">
                    <vxe-input
                      v-model="row.certificateNo"
                      :placeholder="$t('请输入证书编码')"
                      :class="{ 'field-error': hasFieldError(row, 'certificateNo') }"
                      :style="getFieldErrorStyle(row, 'certificateNo')"
                      transfer
                      clearable
                      @input="handleFieldChange(row, 'certificateNo')"
                    />
                  </template>

                  <template #fileNameEdit="{ row }">
                    <div
                      class="file-upload-btn"
                      :class="{ 'field-error': hasFieldError(row, 'fileName') }"
                      :style="getFieldErrorStyle(row, 'fileName')"
                      @click="handleFileUpload(row)"
                    >
                      {{ row.fileName || $t('上传文件') }}
                    </div>
                  </template>

                  <template #fileName="{ row }">
                    <span
                      v-if="row.fileId && row.fileName"
                      class="file-download-link"
                      @click="handleDownload(row)"
                    >
                      {{ row.fileName }}
                    </span>
                    <span v-else>{{ row.fileName || '-' }}</span>
                  </template>

                  <template #effectiveTimeEdit="{ row }">
                    <vxe-input
                      v-model="row.effectiveTime"
                      type="date"
                      :placeholder="$t('请选择生效日期')"
                      :class="{ 'field-error': hasFieldError(row, 'effectiveTime') }"
                      :style="getFieldErrorStyle(row, 'effectiveTime')"
                      transfer
                      clearable
                      @change="handleFieldChange(row, 'effectiveTime')"
                    />
                  </template>

                  <template #failureTimeEdit="{ row }">
                    <vxe-input
                      v-model="row.failureTime"
                      type="date"
                      :placeholder="$t('请选择失效日期')"
                      :class="{ 'field-error': hasFieldError(row, 'failureTime') }"
                      :style="getFieldErrorStyle(row, 'failureTime')"
                      transfer
                      clearable
                      @change="handleFieldChange(row, 'failureTime')"
                    />
                  </template>
                </sc-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传文件弹窗 -->
    <UploadDialog
      v-if="showUploadDialog"
      :modal-data="{ title: $t('上传文件'), cssClass: 'upload-file-dialog' }"
      @confirm-function="handleUploadSuccess"
      @cancel-function="handleUploadCancel"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { collectionListColumns, collectionToolbar } from './config/index'
import UploadDialog from '@/components/Upload/index.vue'
import { download } from '@/utils/utils'

export default {
  name: 'CertificationApplicationFormSupDetail',
  components: { ScTable, UploadDialog },
  data() {
    return {
      currentTabIndex: 0,
      isEditing: false,
      collectionLoading: false,
      formData: {},
      collectionData: [],
      tabList: [{ title: this.$t('详细信息') }],
      collectionColumns: this.getCollectionColumns(),
      collectionToolbar,
      // 认证类型选项（从字典获取）
      certificationTypeOptions: [],
      // 认证类型字典原始数据
      certificationTypeDict: [],
      // 上传相关数据
      showUploadDialog: false,
      uploadingRow: null,
      isUploadingFile: false,
      // 二级物料相关数据
      masterMaterialCode: '', // 从详情数据中获取的物料编码，用于查询二级物料
      secondaryMaterialCache: {}, // 二级物料选项缓存，按认证类型缓存
      // 防抖处理标识
      isProcessing: false
    }
  },

  computed: {
    detailId() {
      return this.$route.query?.id
    },
    // 是否可编辑状态
    canEdit() {
      return ['1', '3'].includes(this.formData.status) && this.formData.status !== '4' // 已发布或已驳回可编辑，已完成不可编辑
    }
  },

  created() {
    // 获取认证类型字典数据
    this.getCertificationTypeDict()
    this.getDetailData()
  },
  activated() {
    this.getDetailData()
  },

  methods: {
    /**
     * 获取表格列配置
     */
    getCollectionColumns() {
      return collectionListColumns
    },

    /**
     * 检查字段是否为必填
     * @param {Object} row - 行数据
     * @param {string} field - 字段名
     * @returns {boolean} 是否必填
     */
    isFieldRequired(row, field) {
      if (!row.authClassType) return false

      const requiredFields = this.getRequiredFieldsByType(row.authClassType)
      return requiredFields.includes(field)
    },

    /**
     * 检查字段是否有错误（必填但未填写）
     * @param {Object} row - 行数据
     * @param {string} field - 字段名
     * @returns {boolean} 是否有错误
     */
    hasFieldError(row, field) {
      // 只有在可编辑状态下才显示错误提示
      if (!this.canEdit) {
        return false
      }

      // 检查是否为必填字段
      const isRequired = this.isFieldRequired(row, field)
      if (!isRequired) {
        return false
      }

      // 检查字段值是否为空
      const fieldValue = row[field]
      const isEmpty = !fieldValue || !fieldValue.toString().trim()

      // 如果字段不为空，直接返回false（无错误）
      if (!isEmpty) {
        return false
      }

      // 逻辑B：如果当前字段为空，但同一认证类型的其他行该字段已完整填写，则不显示红框
      const isSameTypeFieldFilled = this.isFieldFilledBySameType(row, field)
      if (isSameTypeFieldFilled) {
        return false
      }

      return true
    },

    /**
     * 获取字段错误样式
     * @param {Object} row - 行数据
     * @param {string} field - 字段名
     * @returns {Object} 样式对象
     */
    getFieldErrorStyle(row, field) {
      if (this.hasFieldError(row, field)) {
        return {
          border: '1px solid #ff4d4f',
          boxShadow: '0 0 0 2px rgba(255, 77, 79, 0.15)'
        }
      }
      return {}
    },

    /**
     * 字段变化处理，用于实时校验
     * @param {Object} row - 行数据
     * @param {string} field - 字段名
     */
    handleFieldChange(row, field) {
      // 如果是日期字段，进行日期范围校验
      if (
        (field === 'effectiveTime' || field === 'failureTime') &&
        row.effectiveTime &&
        row.failureTime
      ) {
        const dateValidation = this.validateDateRange(row)
        if (!dateValidation.valid) {
          this.$toast({ content: dateValidation.message, type: 'warning' })
        }
      }
      // 强制更新组件以确保样式生效
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },

    /**
     * 物料编码变化处理 - 实现与二级物料的互斥
     * @param {Object} row - 行数据
     */
    handleMaterialCodeChange(row) {
      // 如果填写了物料编码，清空二级物料
      if (row.materialCode && row.materialCode.trim()) {
        row.secondMaterial = ''
        row.secondaryMaterialOptions = []
      }

      // 检查认证类型冲突（如果已选择认证类型）
      if (row.authClassType && row.materialCode && row.materialName) {
        const conflictCheck = this.checkCertificationTypeConflict(row)
        if (!conflictCheck.valid) {
          this.$toast({
            content: conflictCheck.message,
            type: 'warning'
          })
        }
      }

      // 调用通用字段变化处理
      this.handleFieldChange(row, 'materialCode')
    },

    /**
     * 物料名称变化处理 - 实现与二级物料的互斥
     * @param {Object} row - 行数据
     */
    handleMaterialNameChange(row) {
      // 如果填写了物料名称，清空二级物料
      if (row.materialName && row.materialName.trim()) {
        row.secondMaterial = ''
        row.secondaryMaterialOptions = []
      }

      // 检查认证类型冲突（如果已选择认证类型）
      if (row.authClassType && row.materialCode && row.materialName) {
        const conflictCheck = this.checkCertificationTypeConflict(row)
        if (!conflictCheck.valid) {
          this.$toast({
            content: conflictCheck.message,
            type: 'warning'
          })
        }
      }

      // 调用通用字段变化处理
      this.handleFieldChange(row, 'materialName')
    },

    /**
     * 二级物料变化处理 - 实现与物料编码名称的互斥
     * @param {Object} row - 行数据
     */
    handleSecondaryMaterialChange(row) {
      // 如果选择了二级物料，清空物料编码和名称
      if (row.secondMaterial && row.secondMaterial.trim()) {
        row.materialCode = ''
        row.materialName = ''
      }

      // 检查认证类型冲突（如果已选择认证类型和二级物料）
      if (row.authClassType && row.secondMaterial) {
        const conflictCheck = this.checkCertificationTypeConflict(row)
        if (!conflictCheck.valid) {
          this.$toast({
            content: conflictCheck.message,
            type: 'warning'
          })
        }
      }

      // 调用通用字段变化处理
      this.handleFieldChange(row, 'secondMaterial')
    },

    /**
     * 行编辑权限控制
     * @param {Object} param - 参数对象
     * @param {Object} param.row - 行数据
     * @returns {boolean} 是否可编辑
     */
    activeRowMethod({ row }) {
      if (!this.canEdit) {
        return false
      }
      if (row.authFlag === '2') {
        return false // authFlag=2的行完全不可编辑，仅可查看
      }
      return ['1', '3', '4'].includes(row.authFlag)
    },

    /**
     * 编辑开始事件
     * @param {Object} args - 事件参数
     */
    editBegin(args) {
      if (args.$event) {
        // 编辑开始时强制更新组件，确保红框样式正确显示
        this.$nextTick(() => {
          this.$forceUpdate()
        })

        // 自动填充逻辑已改为在输入框获得焦点时触发，此处不再处理
      }
    },

    /**
     * 编辑完成事件
     * @param {Object} args - 事件参数
     */
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 如果正在上传文件，不要退出编辑状态
        if (this.isUploadingFile && this.uploadingRow === row) {
          args.$event.preventDefault()
          return
        }
      }
    },

    /**
     * 编辑禁用事件
     */
    editDisabledEvent() {
      this.$toast({
        content: this.$t('此状态的数据不可编辑'),
        type: 'warning'
      })
    },

    /**
     * 获取字段禁用状态
     * @param {Object} row - 行数据
     * @param {string} field - 字段名
     * @returns {boolean} 是否禁用
     */
    getFieldDisabled(row, field) {
      if (row.authFlag === '2') {
        return true
      }

      // 认证地区字段始终禁用，不允许用户手动编辑
      if (field === 'authRegion') {
        return true
      }

      // 定义基础只读字段：物料编码、名称、认证类型（认证地区已在上方单独处理）
      const basicReadonlyFields = ['materialCode', 'materialName', 'authClassType']

      if (row.authFlag === '1') {
        return basicReadonlyFields.includes(field)
      }
      if (row.authFlag === '3') {
        return basicReadonlyFields.includes(field)
      }
      if (row.authFlag === '4') {
        // 新增行：物料编码和名称可以编辑
        if (field === 'materialCode' || field === 'materialName') {
          return false
        }
        // 二级物料按照旧有逻辑判断
        if (field === 'secondMaterial') {
          return !this.hasSecondaryMaterialAuthFlag()
        }
        // 其他字段都可编辑
        return false
      }

      return false
    },

    /**
     * 统一认证类型处理，将CCC和CQC视为同一类型，将TUV、VDE、CB视为同一类型
     * @param {string} certificationType - 原始认证类型
     * @returns {string} 统一后的认证类型
     */
    normalizeCertificationType(certificationType) {
      if (!certificationType) return ''

      // CCC和CQC视为同一认证类型
      if (['CCC', 'CQC'].includes(certificationType)) {
        return 'CQC-CCC'
      }

      // TUV、VDE、CB视为同一认证类型
      if (['TUV', 'VDE', 'CB'].includes(certificationType)) {
        return 'TUV-VDE-CB'
      }

      return certificationType
    },

    /**
     * 按认证类型分组获取指定authFlag的行数据
     * @param {Array} authFlags - 需要筛选的authFlag数组
     * @returns {Object} 按统一认证类型分组的数据对象
     */
    getRowsByCertificationTypeGroup(authFlags) {
      const targetRows = this.collectionData.filter((row) => authFlags.includes(row.authFlag))
      const groups = {}

      targetRows.forEach((row) => {
        if (!row.authClassType) return

        const normalizedType = this.normalizeCertificationType(row.authClassType)
        if (!groups[normalizedType]) {
          groups[normalizedType] = []
        }
        groups[normalizedType].push(row)
      })

      return groups
    },

    /**
     * 检查同一authFlag和同一认证类型的其他行是否已完整填写指定字段
     * @param {Object} currentRow - 当前行数据
     * @param {string} field - 要检查的字段名
     * @returns {boolean} 同authFlag同类型其他行是否已填写该字段
     */
    isFieldFilledBySameType(currentRow, field) {
      if (!currentRow.authClassType) return false

      const normalizedType = this.normalizeCertificationType(currentRow.authClassType)

      // 获取同一authFlag和同一认证类型的其他行（排除当前行）
      const sameTypeRows = this.collectionData.filter(
        (row) =>
          row.id !== currentRow.id &&
          row.authClassType &&
          row.authFlag === currentRow.authFlag && // 必须是相同authFlag
          this.normalizeCertificationType(row.authClassType) === normalizedType
      )

      if (sameTypeRows.length === 0) return false

      // 检查是否有任何一行该字段已填写且该行的所有必填字段都已填写
      return sameTypeRows.some((row) => {
        // 该字段已填写
        const fieldFilled = row[field] && row[field].toString().trim()
        if (!fieldFilled) return false

        // 该行所有必填字段都已填写
        const requiredFields = this.getRequiredFieldsByType(row.authClassType)
        const allFieldsFilled = requiredFields.every(
          (reqField) => row[reqField] && row[reqField].toString().trim()
        )

        return allFieldsFilled
      })
    },

    /**
     * 根据认证类型获取必填字段
     * @param {string} certificationType - 认证类型
     * @returns {Array} 必填字段数组
     */
    getRequiredFieldsByType(certificationType) {
      if (!certificationType) return []

      const requiredFields = []

      // CQC、CCC类型：制造商/商标(中文)、工厂名称、型号、标准 必填
      if (['CQC', 'CCC'].includes(certificationType)) {
        requiredFields.push('makerCn', 'factoryName', 'model', 'standard')
      }

      // TUV、VDE、CB、UL类型：制造商/商标(英文)、型号、技术参数、标准 必填
      else if (['TUV', 'VDE', 'CB', 'UL'].includes(certificationType)) {
        requiredFields.push('makerEn', 'model', 'technicalParam', 'standard')
      }

      // 对于所有非"随机测试"的认证类型，证书编号、文档名称、证书生效日期、证书失效日期也为必填
      if (certificationType !== '随机测试') {
        requiredFields.push('certificateNo', 'fileName', 'effectiveTime', 'failureTime')
      }

      return requiredFields
    },

    /**
     * 验证日期范围：证书失效日期要晚于证书生效日期
     * @param {Object} row - 行数据
     * @returns {Object} 验证结果 { valid: boolean, message: string }
     */
    validateDateRange(row) {
      if (!row.effectiveTime || !row.failureTime) {
        return { valid: true, message: '' } // 如果日期为空，不进行校验
      }

      const effectiveDate = new Date(row.effectiveTime)
      const failureDate = new Date(row.failureTime)

      if (failureDate <= effectiveDate) {
        return {
          valid: false,
          message: this.$t('证书失效日期必须晚于证书生效日期')
        }
      }

      return { valid: true, message: '' }
    },

    /**
     * 获取二级物料选项
     * @param {Object} row - 行数据
     * @returns {Promise<Array>} 二级物料选项数组
     */
    async getSecondaryMaterialOptions(row) {
      if (!row.authClassType) {
        this.$toast({ content: this.$t('请先选择认证类型'), type: 'warning' })
        return []
      }

      if (!this.masterMaterialCode) {
        this.$toast({ content: this.$t('缺少物料编码信息'), type: 'warning' })
        return []
      }

      try {
        // 设置当前行的加载状态
        this.$set(row, 'loadingSecondaryMaterial', true)

        const res = await this.$API.sendSampleCertification.getSecondMaterialApi({
          authClassType: row.authClassType,
          certificationType: row.authClassType, // 认证类型同authClassType
          materialCode: this.masterMaterialCode,
          partNumber: this.masterMaterialCode // 物料编码同materialCode
        })

        if (res.code === 200 && res.data) {
          return res.data.split(',').map((item) => {
            return {
              label: item.trim(),
              value: item.trim()
            }
          })
        } else {
          return []
        }
      } catch (error) {
        return []
      } finally {
        // 清除当前行的加载状态
        this.$set(row, 'loadingSecondaryMaterial', false)
      }
    },

    /**
     * 认证类型变化处理
     * @param {Object} row - 行数据
     */
    handleCertificationTypeChange(row) {
      // 先进行认证类型冲突校验
      if (row.authClassType) {
        const conflictCheck = this.checkCertificationTypeConflict(row)
        if (!conflictCheck.valid) {
          // 清空认证类型选择
          row.authClassType = ''
          row.authRegion = ''
          // 显示错误提示
          this.$toast({
            content: conflictCheck.message,
            type: 'warning'
          })
          return
        }
      }

      // 根据认证类型itemName，找到字典中对应的itemCode填入认证地区
      if (row.authClassType) {
        // 从原始字典数据中查找匹配的项
        const dictItem = this.certificationTypeDict.find(
          (item) => item.itemCode === row.authClassType
        )

        if (dictItem) {
          // 将字典项的itemCode填入认证地区字段
          row.authRegion = dictItem.itemName
        }
      } else {
        // 如果认证类型为空，清空认证地区
        row.authRegion = ''
      }

      // 清空二级物料选择
      row.secondMaterial = ''
      // 清空当前行的二级物料选项，强制重新获取
      this.$set(row, 'secondaryMaterialOptions', [])
      // 清除该认证类型的缓存
      const cacheKey = `${row.authClassType}_${this.masterMaterialCode}`
      delete this.secondaryMaterialCache[cacheKey]
      // 调用 获取二级物料的方法 - 仅在二级物料字段未被禁用时调用
      if (!this.getFieldDisabled(row, 'secondMaterial')) {
        this.handleSecondaryMaterialFocus(row)
      }
    },

    /**
     * 物料编码获得焦点处理
     * @param {Object} row - 行数据
     */
    handleMaterialCodeFocus(row) {
      this.autoFillMaterialInfoOnFocus(row, 'materialCode')
    },

    /**
     * 物料名称获得焦点处理
     * @param {Object} row - 行数据
     */
    handleMaterialNameFocus(row) {
      this.autoFillMaterialInfoOnFocus(row, 'materialName')
    },

    /**
     * 在焦点获得时自动填充物料信息
     * @param {Object} row - 行数据
     * @param {string} focusField - 获得焦点的字段名
     */
    autoFillMaterialInfoOnFocus(row, focusField) {
      // 检查是否为新增行
      if (row.authFlag !== '4') {
        return
      }

      // 检查二级物料是否有内容，如果有则不自动填充
      if (row.secondMaterial && row.secondMaterial.trim()) {
        return
      }

      // 检查物料编码和名称字段是否可编辑
      const materialCodeEditable = !this.getFieldDisabled(row, 'materialCode')
      const materialNameEditable = !this.getFieldDisabled(row, 'materialName')

      // 如果物料编码和名称都可编辑，且当前为空，则从元数据中查找并自动填充
      if (materialCodeEditable && materialNameEditable && !row.materialCode && !row.materialName) {
        // 从元数据行（authFlag为1、2、3的行）中查找有物料编码和名称的行
        const metadataRow = this.collectionData.find(
          (existingRow) =>
            existingRow.id !== row.id && // 排除当前行
            ['1', '2', '3'].includes(existingRow.authFlag) && // 只从元数据行中查找
            existingRow.materialCode &&
            existingRow.materialCode.trim() &&
            existingRow.materialName &&
            existingRow.materialName.trim()
        )

        if (metadataRow) {
          row.materialCode = metadataRow.materialCode
          row.materialName = metadataRow.materialName

          // 遵循与二级物料的互斥规则：如果填入了物料编码和名称，清空二级物料
          row.secondMaterial = ''
          row.secondaryMaterialOptions = []

          console.log('自动填充物料信息（从元数据行）:', {
            materialCode: row.materialCode,
            materialName: row.materialName,
            sourceRowId: metadataRow.id,
            sourceAuthFlag: metadataRow.authFlag,
            focusField: focusField
          })
        }
      }
    },

    /**
     * 检查认证类型冲突
     * @param {Object} currentRow - 当前操作的行
     * @returns {Object} 校验结果 { valid: boolean, message: string }
     */
    checkCertificationTypeConflict(currentRow) {
      if (!currentRow.authClassType) {
        return { valid: true, message: '' }
      }

      // 获取除当前行外的其他行
      const otherRows = this.collectionData.filter((row) => row.id !== currentRow.id)

      // 检查物料编码+名称冲突
      const materialConflicts = otherRows.filter((row) => {
        return (
          row.authClassType === currentRow.authClassType &&
          row.materialCode &&
          row.materialName &&
          currentRow.materialCode &&
          currentRow.materialName &&
          row.materialCode.trim() === currentRow.materialCode.trim() &&
          row.materialName.trim() === currentRow.materialName.trim()
        )
      })

      if (materialConflicts.length > 0) {
        return {
          valid: false,
          message: this.$t('不能存在认证类型和物料编码相同的数据')
        }
      }

      // 检查二级物料冲突
      const secondaryMaterialConflicts = otherRows.filter((row) => {
        return (
          row.authClassType === currentRow.authClassType &&
          row.secondMaterial &&
          currentRow.secondMaterial &&
          row.secondMaterial.trim() === currentRow.secondMaterial.trim()
        )
      })

      if (secondaryMaterialConflicts.length > 0) {
        return {
          valid: false,
          message: this.$t('不能存在认证类型和二级物料相同的数据')
        }
      }

      return { valid: true, message: '' }
    },

    /**
     * 二级物料下拉框获得焦点时处理
     * @param {Object} row - 行数据
     */
    async handleSecondaryMaterialFocus(row) {
      // 如果二级物料字段被禁用，不调用获取接口
      if (this.getFieldDisabled(row, 'secondMaterial')) {
        return
      }

      if (!row.authClassType) {
        this.$toast({ content: this.$t('请先选择认证类型'), type: 'warning' })
        return
      }

      // 构建缓存键（认证类型+物料编码）
      const cacheKey = `${row.authClassType}_${this.masterMaterialCode}`

      // 如果缓存中已有该认证类型的选项，直接使用
      if (this.secondaryMaterialCache[cacheKey]) {
        this.$set(row, 'secondaryMaterialOptions', this.secondaryMaterialCache[cacheKey])
        return
      }

      // 如果缓存中没有，重新获取并缓存
      const options = await this.getSecondaryMaterialOptions(row)
      // console.log('------->二级认证 options', options)
      this.secondaryMaterialCache[cacheKey] = options
      this.$set(row, 'secondaryMaterialOptions', options)
    },

    /**
     * 验证行数据
     * @param {Object} row - 行数据
     * @returns {boolean} 验证结果
     */
    validateRowData(row) {
      // 基础验证：认证类型必填
      if (!row.authClassType) {
        this.$toast({ content: this.$t('认证类型为必填'), type: 'error' })
        return false
      }

      // 验证行数据
      if (!this.validateRowByType(row)) {
        const requiredFields = this.getRequiredFieldsByType(row.authClassType)
        this.$toast({
          content: this.$t(
            `根据认证类型${row.authClassType}，以下字段为必填：${requiredFields.join('、')}`
          ),
          type: 'error'
        })
        return false
      }

      return true
    },

    /**
     * 获取按钮禁用状态
     */
    getButtonDisabled(code) {
      if (code === 'save') {
        return !this.canEdit || this.formData.status === '4' // 已完成状态不允许保存
      }
      if (code === 'add') {
        return !this.canEdit || this.formData.status === '4' // 已完成状态不允许新增
      }
      if (code === 'remove') {
        return !this.canEdit || this.formData.status === '4' // 已完成状态不允许删除
      }
      return false
    },

    /**
     * 获取认证类型字典数据
     */
    async getCertificationTypeDict() {
      try {
        const res = await this.$API.masterData.getDictCodeSupApi({
          dictCode: 'CERTIFICATE_TYPE_CLASSIFICATE'
        })

        if (res.code === 200 && res.data && res.data.length > 0) {
          // 保存原始字典数据
          this.certificationTypeDict = res.data

          // 转换为组件所需的格式：认证类型显示和存储都使用itemName
          this.certificationTypeOptions = res.data.map((item) => ({
            value: item.itemCode,
            label: item.itemCode
          }))
        } else {
          console.warn('未获取到认证类型字典数据，使用默认数据')
          // 如果字典获取失败，使用默认数据，同时构造对应的字典数据
          this.certificationTypeDict = [
            { itemCode: '中国', itemName: 'CQC' },
            { itemCode: '中国', itemName: 'CCC' },
            { itemCode: '其它', itemName: 'TUV' },
            { itemCode: '其它', itemName: 'VDE' },
            { itemCode: '其它', itemName: 'CB' },
            { itemCode: '北美', itemName: 'UL' },
            { itemCode: '随机测试', itemName: '随机测试' }
          ]
          this.certificationTypeOptions = [
            { value: 'CQC', label: 'CQC' },
            { value: 'CCC', label: 'CCC' },
            { value: 'TUV', label: 'TUV' },
            { value: 'VDE', label: 'VDE' },
            { value: 'CB', label: 'CB' },
            { value: 'UL', label: 'UL' },
            { value: '随机测试', label: '随机测试' }
          ]
        }
      } catch (error) {
        console.error('获取认证类型字典失败:', error)
        // 出错时使用默认数据，同时构造对应的字典数据
        this.certificationTypeDict = [
          { itemCode: '中国', itemName: 'CQC' },
          { itemCode: '中国', itemName: 'CCC' },
          { itemCode: '其它', itemName: 'TUV' },
          { itemCode: '其它', itemName: 'VDE' },
          { itemCode: '其它', itemName: 'CB' },
          { itemCode: '北美', itemName: 'UL' },
          { itemCode: '随机测试', itemName: '随机测试' }
        ]
        this.certificationTypeOptions = [
          { value: 'CQC', label: 'CQC' },
          { value: 'CCC', label: 'CCC' },
          { value: 'TUV', label: 'TUV' },
          { value: 'VDE', label: 'VDE' },
          { value: 'CB', label: 'CB' },
          { value: 'UL', label: 'UL' },
          { value: '随机测试', label: '随机测试' }
        ]
      }
    },

    /**
     * 获取详情数据
     */
    async getDetailData() {
      this.collectionLoading = true
      try {
        const res =
          await this.$API.sendSampleCertification.getCertificationApplicationFormSupDetailApi(
            this.detailId
          )

        if (res.code === 200 && res.data) {
          // 填充基本信息
          this.formData = {
            authApplyNo: res.data.authApplyNo || '',
            code: res.data.authApplyNo || '',
            name: res.data.authName || '',
            sampleApplyNo: res.data.sampleApplyNo || '',
            authRequirement: res.data.authRequirement || '',
            createUserName: res.data.createdBy || '',
            createTime: res.data.createTime || '',
            rejectReason: res.data.rejectReason || '',
            status: res.data.status || '1'
          }

          // 存储物料编码用于查询二级物料
          this.masterMaterialCode = res.data.materialCode || ''

          // 填充收集清单数据
          if (res.data.detailList && Array.isArray(res.data.detailList)) {
            const newCollectionData = res.data.detailList.map((item) => ({
              id: item.id || '',
              secondMaterial: item.secondMaterial || '',
              authRegion: item.authRegion || '',
              authClassType: item.authClassType || '',
              makerCn: item.makerCn || '',
              makerEn: item.makerEn || '',
              factoryName: item.factoryName || '',
              model: item.model || '',
              technicalParam: item.technicalParam || '',
              standard: item.standard || '',
              certificateNo: item.certificateNo || '',
              fileName: item.fileName || '',
              effectiveTime: item.effectiveTime || null,
              failureTime: item.failureTime || null,
              // 为每行初始化二级物料相关字段
              secondaryMaterialOptions: [],
              loadingSecondaryMaterial: false,
              authFlag: item.authFlag || '4',
              materialCode: item.materialCode || '',
              materialName: item.materialName || '',
              fileId: item.fileId || ''
              // materialCode: '',  // 测试用
              // materialName: '',  // 测试用
              // authFlag: '3'  // 测试用
            }))
            console.log('newCollectionData-->', newCollectionData)
            // 使用Vue响应式更新机制
            this.$set(this, 'collectionData', newCollectionData)
          } else {
            this.$set(this, 'collectionData', [])
          }
        }
      } catch (error) {
        console.log(error)
        this.$toast({ content: this.$t('获取详情数据失败'), type: 'error' })
      } finally {
        this.collectionLoading = false
      }
    },

    /**
     * 标签切换
     * @param {number} index - 标签索引
     */
    handleSelectTab(index) {
      this.currentTabIndex = index
    },

    /**
     * 提交
     */
    async handleSubmit() {
      if (this.formData.status === '4') {
        this.$toast({ content: this.$t('已完成状态的数据不允许提交'), type: 'warning' })
        return
      }

      // 验证必填字段
      const validationResult = this.validateRequiredFields()
      if (!validationResult.valid) {
        // 让有必填字段错误的行进入编辑状态并显示红框
        this.setErrorRowsToEditMode()
        this.$toast({ content: validationResult.message, type: 'error' })
        return
      }

      //
      const executeSubmit = async () => {
        try {
          // 先保存数据
          const saveData = {
            id: this.detailId,
            status: this.formData.status,
            detailList: this.collectionData.map((item) => ({
              ...item,
              authFlag: item.authFlag || '4'
            }))
          }
          console.log('saveData----->', saveData)
          debugger
          // 调用保存API
          const saveRes =
            await this.$API.sendSampleCertification.saveCertificationApplicationFormSupApi(saveData)

          if (saveRes.code !== 200) {
            this.$toast({
              content: saveRes.msg || saveRes.message || this.$t('保存失败，无法继续提交'),
              type: 'error'
            })
            return
          }

          // 保存成功后，调用提交API
          const submitRes =
            await this.$API.sendSampleCertification.submitCertificationApplicationFormSupApi({
              ids: [this.detailId]
            })

          if (submitRes.code === 200) {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            // 使用setTimeout确保提示显示后再跳转，提升用户体验
            setTimeout(() => {
              this.goBack()
            }, 1000)
          } else {
            this.$toast({
              content: submitRes.msg || submitRes.message || this.$t('提交失败'),
              type: 'error'
            })
          }
        } catch (error) {
          console.error('提交失败:', error)
          // 改进错误消息提取，兼容不同的错误对象结构
          const errorMessage =
            error?.response?.data?.message || error?.msg || error?.message || this.$t('操作异常')
          this.$toast({ content: errorMessage, type: 'error' })
        }
      }

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否提交？'),
          type: 'warning'
        },
        success: executeSubmit
      })
    },

    /**
     * 收集清单工具栏操作
     * @param {Object} item - 按钮配置
     */
    handleCollectionToolbar(item) {
      // 防抖处理，避免重复点击导致的问题
      if (this.isProcessing) {
        return
      }

      try {
        this.isProcessing = true

        switch (item.code) {
          case 'save':
            this.handleSaveCollection()
            break
          case 'add':
            this.handleAddCollection()
            break
          case 'remove':
            this.handleRemoveCollection()
            break
          default:
            console.log('未知操作:', item.code)
        }
      } catch (error) {
        console.error('工具栏操作错误:', error)
        this.$toast({
          content: this.$t('操作失败，请稍后重试'),
          type: 'error'
        })
      } finally {
        // 使用setTimeout确保操作完成后再重置状态
        setTimeout(() => {
          this.isProcessing = false
        }, 500)
      }
    },

    /**
     * 保存收集清单
     */
    async handleSaveCollection() {
      if (this.formData.status === '4') {
        this.$toast({ content: this.$t('已完成状态的数据不允许保存'), type: 'warning' })
        return
      }

      if (!this.canEdit) {
        this.$toast({ content: this.$t('当前状态不允许保存'), type: 'warning' })
        return
      }

      try {
        // 验证必填字段
        const validationResult = this.validateRequiredFields()
        if (!validationResult.valid) {
          // 让有必填字段错误的行进入编辑状态并显示红框
          this.setErrorRowsToEditMode()
          this.$toast({ content: validationResult.message, type: 'error' })
          return
        }

        // 准备保存参数
        console.log('this.collectionData----->', this.collectionData)
        const saveData = {
          id: this.detailId,
          status: this.formData.status,
          detailList: this.collectionData.map((item) => ({
            ...item,
            authFlag: item.authFlag || '4',
            effectiveTime: !item.effectiveTime ? null : item.effectiveTime,
            failureTime: !item.failureTime ? null : item.failureTime,
            id: item.id || ''
          }))
        }

        console.log('保存数据:', saveData)

        // 调用保存API
        const res = await this.$API.sendSampleCertification.saveCertificationApplicationFormSupApi(
          saveData
        )

        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          // 重置上传状态
          this.isUploadingFile = false
          this.uploadingRow = null
          // 2. 保存成功后重新获取数据
          await this.getDetailData()
        } else {
          this.$toast({ content: res.message || this.$t('保存失败'), type: 'error' })
        }
      } catch (error) {
        this.$toast({ content: error.msg, type: 'error' })
      }
    },

    /**
     * 检查元数据中是否存在authFlag为2或3的行
     * @returns {boolean} 是否存在authFlag为2或3的行
     */
    hasSecondaryMaterialAuthFlag() {
      return this.collectionData.some((row) => ['2', '3'].includes(row.authFlag))
    },

    /**
     * 分析现有数据类型
     * @returns {Object} 分析结果
     */
    analyzeExistingDataType() {
      if (this.collectionData.length === 0) {
        return {
          hasItemCode: false,
          hasSecondaryMaterial: false,
          itemCodeData: null,
          secondaryMaterialData: null,
          referenceData: null
        }
      }

      // 分析是否有物料编码：找到第一个有物料编码的行
      const itemCodeRow = this.collectionData.find(
        (item) => item.materialCode && item.materialCode.trim()
      )
      const hasItemCode = !!itemCodeRow

      // 分析是否包含二级物料：找到第一个有二级物料的行
      const secondaryMaterialRow = this.collectionData.find(
        (item) => item.secondMaterial && item.secondMaterial.trim()
      )
      const hasSecondaryMaterial = !!secondaryMaterialRow

      return {
        hasItemCode,
        hasSecondaryMaterial,
        itemCodeData: itemCodeRow,
        secondaryMaterialData: secondaryMaterialRow,
        referenceData: itemCodeRow || secondaryMaterialRow || this.collectionData[0]
      }
    },

    /**
     * 生成新行数据
     * @returns {Object} 新行数据
     */
    generateNewRowData() {
      const newRow = {
        id: '',
        materialCode: '',
        materialName: '',
        secondMaterial: '',
        authRegion: '',
        authClassType: '',
        makerCn: '',
        makerEn: '',
        factoryName: '',
        model: '',
        technicalParam: '',
        standard: '',
        certificateNo: '',
        fileName: '',
        fileId: '',
        effectiveTime: null,
        failureTime: null,
        authFlag: '4',
        // 为新增行初始化二级物料相关字段
        secondaryMaterialOptions: [],
        loadingSecondaryMaterial: false
      }

      // 新增行不从元数据带入任何数据，保持为空

      return newRow
    },

    /**
     * 添加收集清单
     */
    handleAddCollection() {
      if (this.formData.status === '4') {
        this.$toast({ content: this.$t('已完成状态的数据不允许新增'), type: 'warning' })
        return
      }

      if (!this.canEdit) {
        this.$toast({ content: this.$t('当前状态不允许新增'), type: 'warning' })
        return
      }

      const newRow = this.generateNewRowData()
      this.collectionData.push(newRow)

      this.$toast({ content: this.$t('新增行成功'), type: 'success' })

      // 自动进入编辑状态
      this.$nextTick(() => {
        this.$refs.collectionTableRef.$refs.xGrid.setEditRow(newRow)
      })
    },

    /**
     * 移除收集清单
     */
    handleRemoveCollection() {
      if (this.formData.status === '4') {
        this.$toast({ content: this.$t('已完成状态的数据不允许删除'), type: 'warning' })
        return
      }

      if (!this.canEdit) {
        this.$toast({ content: this.$t('当前状态不允许删除'), type: 'warning' })
        return
      }

      const selectedRecords = this.$refs.collectionTableRef?.$refs.xGrid?.getCheckboxRecords() || []
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请选择要删除的记录'), type: 'warning' })
        return
      }

      // 检查是否只能删除新增的数据行（authFlag为4的行）
      const canDeleteRecords = selectedRecords.filter((record) => record.authFlag === '4')

      if (canDeleteRecords.length === 0) {
        this.$toast({ content: this.$t('只能删除新增的数据行，元数据不允许移除'), type: 'warning' })
        return
      }

      if (canDeleteRecords.length !== selectedRecords.length) {
        this.$toast({
          content: this.$t('选中的记录中包含元数据，元数据不允许移除'),
          type: 'warning'
        })
        return
      }

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认删除选中的${canDeleteRecords.length}条记录？`),
          type: 'warning'
        },
        success: () => {
          // 删除选中的记录
          canDeleteRecords.forEach((record) => {
            const index = this.collectionData.findIndex((item) => item.id === record.id)
            if (index > -1) {
              this.collectionData.splice(index, 1)
            }
          })
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
        }
      })
    },

    /**
     * 让有必填字段错误的行进入编辑状态并显示红框
     */
    setErrorRowsToEditMode() {
      const errorRows = []

      // 遍历所有数据行，找出有必填字段错误的行
      this.collectionData.forEach((row) => {
        // 只检查可编辑的行
        if (['1', '3', '4'].includes(row.authFlag)) {
          const hasError = this.hasRowFieldErrors(row)
          if (hasError) {
            errorRows.push(row)
          }
        }
      })

      if (errorRows.length > 0) {
        // 确保表格处于编辑模式
        this.isEditing = true

        // 先强制更新一次，确保响应式数据同步
        this.$forceUpdate()

        // 让有错误的行进入编辑状态
        this.$nextTick(() => {
          const tableRef = this.$refs.collectionTableRef
          if (tableRef && tableRef.$refs.xGrid) {
            // 先清除所有行的编辑状态，避免冲突
            tableRef.$refs.xGrid.clearEdit()

            setTimeout(() => {
              // 让第一行有错误的行进入编辑状态（VXE表格每次只能编辑一行）
              const firstErrorRow = errorRows[0]
              tableRef.$refs.xGrid.setEditRow(firstErrorRow)

              // 再次强制更新组件以确保红框样式生效
              this.$nextTick(() => {
                this.$forceUpdate()
              })
            }, 200)
          }
        })
      }
    },

    /**
     * 检查行是否有必填字段错误
     * @param {Object} row - 行数据
     * @returns {boolean} 是否有必填字段错误
     */
    hasRowFieldErrors(row) {
      if (!row.authClassType) {
        return true
      }

      // 获取该行认证类型的必填字段
      const requiredFields = this.getRequiredFieldsByType(row.authClassType)

      // 检查是否有任何必填字段错误
      return requiredFields.some((field) => this.hasFieldError(row, field))
    },

    /**
     * 验证必填字段
     */
    validateRequiredFields() {
      const errors = []

      // 获取所有可编辑的行数据
      const flag1Rows = this.collectionData.filter((row) => row.authFlag === '1')
      const flag3Rows = this.collectionData.filter((row) => row.authFlag === '3')
      const flag4Rows = this.collectionData.filter((row) => row.authFlag === '4')

      // 统一按认证类型分组校验所有可编辑行（包括authFlag=1,3,4）
      const allEditableRows = [...flag1Rows, ...flag3Rows, ...flag4Rows]

      if (allEditableRows.length === 0) {
        return { valid: true, message: '' }
      }

      // 按认证类型分组所有可编辑行
      const certificationGroups = {}
      allEditableRows.forEach((row) => {
        if (!row.authClassType) {
          // 处理认证类型为空的情况
          const rowDesc = this.getRowDescription(row)
          errors.push(`${rowDesc}：认证类型为必填`)
          return
        }

        const normalizedType = this.normalizeCertificationType(row.authClassType)
        if (!certificationGroups[normalizedType]) {
          certificationGroups[normalizedType] = []
        }
        certificationGroups[normalizedType].push(row)
      })

      // 对每个认证类型分组进行校验
      Object.keys(certificationGroups).forEach((normalizedType) => {
        const groupRows = certificationGroups[normalizedType]

        // 检查该认证类型组内是否至少有一行必填字段完整填写
        const hasValidRow = groupRows.some((row) => this.validateRowByType(row))

        if (!hasValidRow) {
          // 检查是否有日期校验错误
          const hasDateError = groupRows.some((row) => {
            if (row.authClassType !== '随机测试') {
              const dateValidation = this.validateDateRange(row)
              return !dateValidation.valid
            }
            return false
          })

          // 获取该组的实际认证类型名称（用于错误提示）
          const actualTypes = [...new Set(groupRows.map((row) => row.authClassType))]
          const typeDisplay = actualTypes.join('/')

          // 分析该组包含的行类型，生成合适的错误描述
          const groupDesc = this.getGroupDescription(groupRows)

          if (hasDateError) {
            errors.push(
              `${groupDesc}中认证类型"${typeDisplay}"存在日期范围错误：证书失效日期必须晚于证书生效日期`
            )
          } else {
            errors.push(`${groupDesc}中认证类型"${typeDisplay}"至少需要有一行必填字段完整填写`)
          }
        }
      })

      return {
        valid: errors.length === 0,
        message: errors.join('；')
      }
    },

    /**
     * 获取行描述（用于错误提示）
     * @param {Object} row - 行数据
     * @returns {string} 行描述
     */
    getRowDescription(row) {
      if (row.authFlag === '1') {
        return '一级物料编辑行'
      } else if (row.authFlag === '3') {
        return '二级物料编辑行'
      } else if (row.authFlag === '4') {
        // 获取新增行的序号
        const flag4Rows = this.collectionData.filter((r) => r.authFlag === '4')
        const index = flag4Rows.findIndex((r) => r.id === row.id)
        return `新增行${index + 1}`
      }
      return '数据行'
    },

    /**
     * 获取分组描述（用于错误提示）
     * @param {Array} groupRows - 分组行数据
     * @returns {string} 分组描述
     */
    getGroupDescription(groupRows) {
      const flag1Count = groupRows.filter((row) => row.authFlag === '1').length
      const flag3Count = groupRows.filter((row) => row.authFlag === '3').length
      const flag4Count = groupRows.filter((row) => row.authFlag === '4').length

      const descriptions = []
      if (flag1Count > 0) descriptions.push('一级物料编辑行')
      if (flag3Count > 0) descriptions.push('二级物料编辑行')
      if (flag4Count > 0) descriptions.push('新增行')

      return descriptions.join('、')
    },

    /**
     * 根据认证类型验证行数据
     * @param {Object} row - 行数据
     * @returns {boolean} 验证结果
     */
    validateRowByType(row) {
      if (!row.authClassType) return false

      // 使用新的getRequiredFieldsByType方法获取必填字段
      const requiredFields = this.getRequiredFieldsByType(row.authClassType)

      // 检查必填字段是否都已填写
      const fieldsValid = requiredFields.every((field) => row[field] && row[field].trim())

      // 如果基础字段校验失败，直接返回false
      if (!fieldsValid) return false

      // 如果是非随机测试类型，还需要校验日期范围
      if (row.authClassType !== '随机测试') {
        const dateValidation = this.validateDateRange(row)
        if (!dateValidation.valid) return false
      }

      return true
    },

    /**
     * 处理文件上传
     * @param {Object} row - 行数据
     */
    handleFileUpload(row) {
      this.uploadingRow = row
      this.isUploadingFile = true
      this.showUploadDialog = true
    },

    /**
     * 上传成功处理
     * @param {Array} uploadData - 上传返回的数据
     */
    handleUploadSuccess(uploadData) {
      console.log('uploadData----->', uploadData)
      if (uploadData) {
        const fileInfo = uploadData
        if (this.uploadingRow) {
          // 记住当前上传的行，用于重新触发编辑
          const currentUploadingRow = this.uploadingRow

          // 更新文件信息
          this.uploadingRow.fileName = fileInfo.fileName
          this.uploadingRow.fileId = fileInfo.id

          this.$toast({ content: this.$t('文件上传成功'), type: 'success' })

          // 重置上传状态
          this.showUploadDialog = false
          this.isUploadingFile = false
          this.uploadingRow = null

          // 延迟重新触发编辑状态，让用户可以继续编辑其他字段
          this.$nextTick(() => {
            setTimeout(() => {
              const tableRef = this.$refs.collectionTableRef
              if (tableRef && tableRef.$refs.xGrid && currentUploadingRow) {
                // 重新激活该行的编辑状态
                tableRef.$refs.xGrid.setEditRow(currentUploadingRow)
              }
            }, 100) // 延迟100ms确保编辑状态切换完成
          })
        }
      } else {
        // 如果上传失败，也要重置状态
        this.showUploadDialog = false
        this.isUploadingFile = false
        this.uploadingRow = null
      }
    },

    /**
     * 上传取消处理
     */
    handleUploadCancel() {
      console.log('我触发了-----》')
      // 记住当前上传的行，用于重新触发编辑
      const currentUploadingRow = this.uploadingRow

      // 重置上传状态
      this.showUploadDialog = false
      this.isUploadingFile = false
      this.uploadingRow = null

      // 重新激活该行的编辑状态，确保用户可以继续操作
      if (currentUploadingRow) {
        this.$nextTick(() => {
          setTimeout(() => {
            const tableRef = this.$refs.collectionTableRef
            if (tableRef && tableRef.$refs.xGrid) {
              // 重新激活该行的编辑状态
              tableRef.$refs.xGrid.setEditRow(currentUploadingRow)
            }
          }, 100) // 延迟100ms确保状态切换完成
        })
      }
    },

    /**
     * 下载文件
     * @param {Object} row - 行数据
     */
    handleDownload(row) {
      if (!row.fileId) {
        this.$toast({ content: this.$t('文件不存在'), type: 'warning' })
        return
      }

      this.$loading()
      this.$API.fileService
        .downloadPrivateFile({ id: row.fileId })
        .then((res) => {
          this.$hloading()
          download({
            fileName: row.fileName,
            blob: new Blob([res.data])
          })
        })
        .catch((error) => {
          this.$hloading()
          console.error('文件下载失败:', error)
          this.$toast({ content: this.$t('文件下载失败'), type: 'error' })
        })
    },

    /**
     * 返回
     */
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-fix-wrap {
  padding: 20px;

  .detail-header-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 10px;
    min-height: 32px;
    gap: 10px;
  }

  .bottom-tables {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .bottom-box {
      flex: 1;
      overflow: auto;
    }
  }

  .detail-content {
    width: 100%;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    padding: 20px;
  }

  .form-layout {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px 16px;
    align-items: start;

    .form-item {
      display: flex;
      flex-direction: row;
      align-items: center;

      &.form-item-wide {
        grid-column: span 2;
      }
    }

    .form-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      width: 120px;
      flex-shrink: 0;
      margin-right: 12px;
      margin-bottom: 0;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.5;
    }

    .form-value {
      flex: 1;
      min-height: 32px;
      line-height: 1.6;
      font-size: 14px;
      color: #232b39;
      padding: 8px 12px;
      word-break: break-all;
      display: flex;
      align-items: center;

      &.reject-reason {
        background: #fff2f0;
        border-color: #ffccc7;
        color: #ff4d4f;
        font-weight: 500;
      }
    }
  }

  .info-section {
    margin-bottom: 20px;

    .section-title {
      padding: 12px 16px;
      background: #f5f5f5;
      border: 1px solid #e8e8e8;
      border-radius: 4px 4px 0 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-text {
        flex-shrink: 0;
      }

      .title-tip {
        font-size: 14px;
        font-weight: 600;
        color: #ff4d4f;
        margin-left: 16px;
        line-height: 1.4;
      }
    }

    .section-content {
      border: 1px solid #e8e8e8;
      border-top: none;
      border-radius: 0 0 4px 4px;
      padding: 20px;
      background: #fff;
    }
  }

  .collection-list {
    margin-top: 0;
  }
}

/deep/ .mt-tabs {
  .mt-tabs-container {
    width: 100%;
  }
}

// 必填字段错误状态样式
/deep/ .field-error {
  border: 1px solid #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.15) !important;

  &:focus {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
  }
}

// 文件上传按钮样式
.file-upload-btn {
  cursor: pointer;
  color: #007acc;
  text-decoration: underline;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  min-height: 32px;
  line-height: 20px;
  display: flex;
  align-items: center;
  transition: all 0.3s;

  &:hover {
    color: #40a9ff;
    border-color: #40a9ff;
    background: #f6ffed;
  }

  &.field-error {
    border: 1px solid #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.15) !important;
  }
}

// 驳回原因样式
.reject-reason {
  color: #ff4d4f;
  font-weight: 500;
}

// 下载链接样式
.file-download-link {
  color: #007acc;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: #40a9ff;
    text-decoration: none;
  }
}
</style>
