/**
 * 认证申请单搜索表单配置
 */
import { i18n } from '@/main.js'
import { statusOptions } from './index'

export const getSearchFormItems = () => [
  {
    label: i18n.t('认证申请单号'),
    prop: 'authApplyNo',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('名称'),
    prop: 'authName',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('公司'),
    prop: 'company',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('物料编码'),
    prop: 'materialCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('创建者'),
    prop: 'createdBy',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('创建时间'),
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('状态'),
    prop: 'status',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      dataSource: statusOptions
    }
  }
]
