import { i18n } from '@/main.js'

// 状态
export const statusOptions = [
  { value: 1, text: i18n.t('已发布') },
  { value: 2, text: i18n.t('待审阅') },
  { value: 3, text: i18n.t('已驳回') },
  { value: 4, text: i18n.t('已完成') }
]

// 按钮
export const toolbar = []

export const columnData = [
  {
    width: 50,
    type: 'checkbox',
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'company',
    title: i18n.t('公司'),
    minWidth: 120,
    formatter: ({ row }) => {
      if (row.company && row.companyCode) {
        return `${row.companyCode}-${row.company}`
      }
      return row.company || ''
    }
  },
  {
    field: 'authApplyNo',
    title: i18n.t('认证申请单号'),
    minWidth: 140,
    slots: { default: 'authApplyNo' }
  },
  {
    field: 'authName',
    title: i18n.t('名称')
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称')
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称')
  },
  {
    field: 'statusName',
    title: i18n.t('状态')
  },
  {
    field: 'rejectReason',
    title: i18n.t('驳回原因'),
    minWidth: 150
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN')
    }
  }
]

// 详情页面收集清单表格配置
export const collectionListColumns = [
  {
    width: 50,
    type: 'checkbox',
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 60,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'materialCodeEdit'
    }
  },
  {
    field: 'materialName',
    title: i18n.t('名称'),
    minWidth: 150,
    editRender: {},
    slots: {
      edit: 'materialNameEdit'
    }
  },
  {
    field: 'secondaryMaterial',
    title: i18n.t('二级物料'),
    minWidth: 120,
    editRender: {},
    slots: {
      default: 'secondaryMaterial',
      edit: 'secondaryMaterialEdit'
    }
  },
  {
    field: 'authRegion',
    title: i18n.t('认证地区'),
    minWidth: 100,
    editRender: {},
    slots: {
      edit: 'authRegionEdit'
    }
  },
  {
    field: 'authClassType',
    title: i18n.t('认证类型'),
    minWidth: 100,
    editRender: {},
    slots: {
      edit: 'authClassTypeEdit'
    }
  },
  {
    field: 'makerCn',
    title: i18n.t('制造商/商标(中文)'),
    minWidth: 150,
    editRender: {},
    slots: {
      edit: 'makerCnEdit'
    }
  },
  {
    field: 'makerEn',
    title: i18n.t('制造商/商标(英文)'),
    minWidth: 150,
    editRender: {},
    slots: {
      edit: 'makerEnEdit'
    }
  },
  {
    field: 'factoryName',
    title: i18n.t('工厂名称'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'factoryNameEdit'
    }
  },
  {
    field: 'model',
    title: i18n.t('型号'),
    minWidth: 100,
    editRender: {},
    slots: {
      edit: 'modelEdit'
    }
  },
  {
    field: 'technicalParam',
    title: i18n.t('技术参数'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'technicalParamEdit'
    }
  },
  {
    field: 'standard',
    title: i18n.t('标准'),
    minWidth: 100,
    editRender: {},
    slots: {
      edit: 'standardEdit'
    }
  },
  {
    field: 'certificateNo',
    title: i18n.t('证书编码'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'certificateNoEdit'
    }
  },
  {
    field: 'fileName',
    title: i18n.t('文档名称'),
    minWidth: 120,
    editRender: {},
    slots: {
      default: 'fileName',
      edit: 'fileNameEdit'
    }
  },
  {
    field: 'effectiveTime',
    title: i18n.t('生效日期'),
    minWidth: 100,
    editRender: {},
    slots: {
      edit: 'effectiveTimeEdit'
    }
  },
  {
    field: 'failureTime',
    title: i18n.t('失效日期'),
    minWidth: 100,
    editRender: {},
    slots: {
      edit: 'failureTimeEdit'
    }
  }
]

// 收集清单工具栏
export const collectionToolbar = [
  {
    code: 'add',
    name: i18n.t('添加'),
    status: 'primary',
    loading: false
  },
  {
    code: 'remove',
    name: i18n.t('移除'),
    status: 'danger',
    loading: false
  },
  {
    code: 'save',
    name: i18n.t('保存'),
    status: 'primary',
    loading: false
  }
]
