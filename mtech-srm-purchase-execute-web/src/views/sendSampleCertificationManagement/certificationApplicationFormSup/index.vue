<!--
 * @Author: wb_qianxiaoxia <EMAIL>
 * @Date: 2025-06-17 21:55:50
 * @LastEditors: wb_qianxiaoxia <EMAIL>
 * @LastEditTime: 2025-06-19 14:04:04
 * @FilePath: /mtech-srm-purchase-execute-web/src/views/sendSampleCertificationManagement/certificationApplicationFormSup/index.vue
 * @Description: 供方-认证申请单
-->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      gird-id="bc5101eb-e5d5-4235-8f21-8150f63ef072"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #authApplyNo="{ row }">
        <span class="link-text" @click="handleViewDetail(row)">{{ row.authApplyNo }}</span>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, toolbar } from './config/index'
import { getSearchFormItems } from './config/searchForm'
import dayjs from 'dayjs'

export default {
  name: 'CertificationApplicationFormSup',
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      columns: columnData,
      toolbar: toolbar,
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems()
      // 为创建时间字段添加onChange事件处理
      const dateFields = ['createTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },

  created() {
    this.getTableData()
  },
  activated() {
    this.getTableData()
  },

  methods: {
    /**
     * 处理工具栏按钮点击事件
     * @param {Object} item - 按钮配置
     */
    handleClickToolBar(item) {
      switch (item.code) {
        case 'submit':
          this.handleSubmitSelected()
          break
        default:
          console.log('未知操作:', item.code)
      }
    },

    /**
     * 处理选中数据提交
     */
    async handleSubmitSelected() {
      // 获取选中的记录
      const selectedRecords = this.tableRef?.getCheckboxRecords() || []

      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一条数据'), type: 'warning' })
        return
      }

      // 提取选中记录的 ID
      const selectedIds = selectedRecords.map((record) => record.id)

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认提交选中的 ${selectedRecords.length} 条认证申请单？`),
          type: 'warning'
        },
        success: async () => {
          try {
            const res =
              await this.$API.sendSampleCertification.submitCertificationApplicationFormSupApi({
                ids: selectedIds
              })

            if (res.code === 200) {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              // 清除选中状态
              this.tableRef?.clearCheckboxRow()
              // 刷新表格数据
              this.handleSearch()
            } else {
              this.$toast({ content: res.message || this.$t('提交失败'), type: 'error' })
            }
          } catch (error) {
            console.error('批量提交认证申请单失败:', error)
            this.$toast({ content: this.$t('提交失败'), type: 'error' })
          }
        }
      })
    },

    /**
     * 处理日期时间字段change事件
     * @param {Array|Object} dateRange - 日期范围数组或对象
     * @param {string} field - 字段名
     */
    handleDateTimeChange(dateRange, field) {
      // 处理不同格式的日期范围输入
      if (dateRange && Array.isArray(dateRange) && dateRange.length === 2) {
        // 数组格式的日期范围
        this.searchFormModel[`${field}S`] = dayjs(dateRange[0]).valueOf()
        this.searchFormModel[`${field}E`] = dayjs(dateRange[1]).valueOf()
      } else if (dateRange && dateRange.startDate) {
        // 对象格式的日期范围，转换为时间戳
        this.searchFormModel[`${field}S`] = dayjs(dateRange.startDate).startOf('day').valueOf()
        this.searchFormModel[`${field}E`] = dayjs(dateRange.endDate).endOf('day').valueOf()
      } else {
        // 清空日期范围
        this.searchFormModel[`${field}S`] = undefined
        this.searchFormModel[`${field}E`] = undefined
      }
    },

    /**
     * 获取表格数据
     */
    async getTableData() {
      this.loading = true
      try {
        const params = {
          ...this.searchFormModel,
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          }
        }

        // 移除不需要传给后端的字段
        delete params.createTime

        const res =
          await this.$API.sendSampleCertification.pageQueryCertificationListForSupplierApi(params)
        if (res.code === 200) {
          this.tableData = res.data?.records || []
          this.pageSettings.totalPages = res.data?.pages ? +res.data.pages : 0
          this.pageSettings.totalRecordsCount = res.data?.total ? +res.data.total : 0
        } else {
          this.$toast({ content: res.message || this.$t('查询失败'), type: 'error' })
        }
      } catch (error) {
        console.error('查询认证申请单失败:', error)
        this.$toast({ content: this.$t('查询失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    /**
     * 重置
     */
    handleReset() {
      this.searchFormModel = {}
      this.$refs.searchFormRef?.resetFields()
      this.currentPage = 1
      this.getTableData()
    },

    /**
     * 页码变化
     * @param {number} page - 当前页码
     */
    handleCurrentChange(page) {
      this.currentPage = page
      this.getTableData()
    },

    /**
     * 页面大小变化
     * @param {number} size - 页面大小
     */
    handleSizeChange(size) {
      this.pageSettings.pageSize = size
      this.currentPage = 1
      this.getTableData()
    },

    handleViewDetail(row) {
      this.$router.push({
        path: '/purchase-execute/certification-management-detail-sup',
        query: { id: row.id }
      })
    }
  }
}
</script>

<style scoped>
.toggle-container {
  margin-bottom: 16px;
}
.link-text {
  color: #1890ff;
  cursor: pointer;
  text-decoration: underline;
}
</style>
