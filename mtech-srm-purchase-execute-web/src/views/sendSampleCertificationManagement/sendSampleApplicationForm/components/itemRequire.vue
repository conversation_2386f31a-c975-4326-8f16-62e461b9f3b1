<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="dataInfo.sampleItems"
      height="300px"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :fix-height="300"
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="false"
      @refresh="getTableData"
      @edit-closed="editComplete"
      :checkbox-config="{ checkMethod: isCheckable }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>

      <template #moldHistoryDefault="{ row, column }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleCheck(row, column)">
            {{ row[column.field] }}
          </span>
        </div>
      </template>

      <template #uploadFile="{ row, column }">
        <div>
          <div class="uipload-box">
            <div id="drop" class="droparea">
              <div class="click-upbox" id="browse">
                <template v-if="row[column.field]">
                  <span class="file-info">
                    <span class="file-name" @click="handleCheckUpload(row, column)">
                      {{ row[column.field] }}
                    </span>
                    <!-- <label class="reupload-label">
                      重新上传
                      <input
                        type="file"
                        class="upload-input"
                        @change="(event) => chooseFiles(event, row)"
                      />
                    </label> -->
                  </span>
                </template>
                <!-- <label v-else class="upload-label">
                  上传
                  <input
                    type="file"
                    class="upload-input"
                    @change="(event) => chooseFiles(event, row)"
                  />
                </label> -->
              </div>
            </div>
          </div>
        </div>
      </template>
    </sc-table>

    <deliver
      @handleAddDialogShow="deliveryShow = false"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import deliver from './deliver.vue'
import ScTable from '@/components/ScTable/src/index'
export default {
  name: 'ItemTab',
  components: { ScTable, deliver },
  props: {
    dataInfo: {
      type: Object,
      default: () => ({
        sampleItems: []
      })
    }
  },
  data() {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    return {
      userInfo,
      selectedRecords: [],
      deliveryShow: false,
      loading: false,
      tableData: [],
      editRules: {
        shippedQuantity: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'qualificationItemName',
          title: this.$t('资质项名称')
        },
        {
          field: 'fileName',
          width: 300,
          title: this.$t('供应商上传资质项文件'),
          slots: {
            default: 'uploadFile'
          }
        },
        {
          field: 'remark',
          title: this.$t('备注')
        },
        {
          field: 'docName',
          title: this.$t('资质项附件模板'),
          slots: {
            default: 'moldHistoryDefault'
          }
        },
        {
          field: 'approveTypeName',
          title: this.$t('审批类型')
        },
        {
          field: 'approveUserName',
          title: this.$t('审批人')
        },
        {
          field: 'passFlag',
          title: this.$t('是否通过'),
          editRender: {},
          slots: {
            default: ({ row }) => [
              row.approveUserId == this.userInfo.externalCode &&
              this.dataInfo.statusName != '已完成' ? (
                <mt-select
                  v-model={row.passFlag}
                  data-source={[
                    { value: '是', text: '是', cssClass: 'col-active' },
                    { value: '否', text: '否', cssClass: 'col-active' }
                  ]}
                  fields={{ text: 'text', value: 'value' }}
                  show-clear-button={true}
                  allow-filtering={true}
                  filter-type='Contains'
                />
              ) : (
                row.passFlag
              )
            ]
          }
        },
        {
          field: 'rejectReason',
          title: this.$t('驳回原因'),
          editRender: {},
          slots: {
            default: ({ row }) => [
              row.approveUserId == this.userInfo.externalCode &&
              this.dataInfo.statusName != '已完成'
                ? row.passFlag === '否' && (
                    <mt-input
                      v-model={row.rejectReason}
                      filter-type='Contains'
                      key={`input-${row.passFlag}`}
                    />
                  )
                : row.rejectReason
            ]
          }
        },
        {
          field: 'updateTime',
          title: this.$t('更新时间')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        }
        // {
        //   field: 'shippedQuantity',
        //   title: this.$t('发货数量'),
        //   editRender: {},
        //   slots: {
        //     edit: ({ row }) => {
        //       return [
        //         <vxe-input
        //           type='integer'
        //           v-model={row.shippedQuantity}
        //           placeholder={this.$t('请输入')}
        //           min='1'
        //           max={row.planDeliveryQty}
        //           transfer
        //           clearable
        //         />
        //       ]
        //     }
        //   }
        // },
        // {
        //   field: 'baseUnitCode',
        //   title: this.$t('单位'),
        //   formatter: ({ cellValue, row }) => {
        //     return cellValue ? cellValue + '-' + row.baseUnitName : ''
        //   }
        // },
      ]
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = [
        { code: 'refuse', name: this.$t('驳回'), status: 'info', loading: false },
        { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
        { code: 'viewDocument', name: this.$t('图纸查询'), status: 'info', loading: false }
      ]
      return btns
    }
  },
  mounted() {
    // if (this.pageType === 'create') {
    //   let data = []
    //   data = sessionStorage.getItem('deliveryPlanSelectedRecords')
    //     ? JSON.parse(sessionStorage.getItem('deliveryPlanSelectedRecords'))
    //     : []
    //   this.tableData = data.map((item) => {
    //     return {
    //       planId: item.id,
    //       deliveryPlanNo: item.deliveryPlanCode,
    //       supplierTenantId: item.supplierTenantId,
    //       supplierCode: item.supplierCode,
    //       supplierName: item.supplierName,
    //       itemCode: item.itemCode,
    //       itemName: item.itemName,
    //       baseUnitCode: item.unitCode,
    //       baseUnitName: item.unit,
    //       categoryCode: item.catoryCode,
    //       categoryName: item.catoryName,
    //       planDeliveryQty: Number(item.planDeliveryQty),
    //       shippedQuantity: Number(item.planDeliveryQty)
    //     }
    //   })
    //   this.$emit('updateDetail', this.tableData)
    // } else {
    //   this.getTableData()
    // }
  },
  methods: {
    chooseFiles(event, row) {
      let _files = event.target.files
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择需要上传的文件.')
        })
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('UploadFiles', _files[i])
        if (_files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$store.commit('startLoading')
      this.$API.fileService.uploadPrivateFile(_data).then((res) => {
        this.$store.commit('endLoading')
        row.fileId = res.data.id
        row.fileName = res.data.fileName
        row.fileUrl = res.data.url
        // this.$emit('confirm-function', res?.data)
      })
    },

    // 下载
    async handleCheck(row) {
      const res = await this.$API.sendSampleCertification.getPlmFileUrl(row.docId)
      if (res.code == 200 && res.data) {
        window.open(res.data)
      }
    },
    async handleCheckUpload(row) {
      const res = await this.$API.sendSampleCertification.getPlmFileUrl(row.fileId)
      if (res.code == 200 && res.data) {
        window.open(res.data)
      }

      // this.$API.fileService
      //   .downloadPublicFile({
      //     id: row.fileId
      //   })
      //   .then((res) => {
      //     download({
      //       fileName: row.fileName,
      //       blob: res.data
      //     })
      //   })
    },
    /**
     * 获取表格数据
     */
    async getTableData() {
      try {
        this.loading = true
        let params = {
          ids: [this.$route.query?.id]
        }
        const res = await this.$API.supplierVmiManagement.detailInventoryManagementApi(params)
        if (res.code === 200) {
          this.tableData = res.data
          this.$emit('updateDetail', this.tableData)
        } else {
          this.$toast({ content: res.msg || this.$t('获取数据失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    isCheckable({ row }) {
      return row.approveUserId == this.userInfo.externalCode
    },

    resolveClick(reason) {
      console.log('数据')
      console.log(reason)
      console.log(this.selectedRecords)
      // const res = await this.$API.sendSampleCertification.plmSampleApplyReject({ ids })
      this.deliveryShow = false
    },

    /**
     * 处理工具栏按钮点击
     */
    async handleClickToolBar(e) {
      if (e.code == 'viewDocument') {
        this.$router.push({
          name: 'drawings-check',
          query: {
            itemCode: this.dataInfo.materialCode,
            timeStamp: new Date().getTime()
          }
        })

        return
      }

      // plmSampleApplyConfirmData
      if (e.code == 'save') {
        const res = await this.$API.sendSampleCertification.plmSampleApplyConfirmData(this.dataInfo)
        if (res.code == 200 && res.data) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          // this.$router.go(-1)
          // this.$router.push({
          //   name: 'send-sample-management',
          //   query: {
          //     timeStamp: new Date().getTime()
          //   }
          // })
        }
        // eslint-disable-next-line no-dupe-else-if
      } else if (e.code == 'refuse') {
        const selectedRecords = this.tableRef.getCheckboxRecords()
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        console.log('选中的数据')
        const allPassFlagsFalse = selectedRecords.every(
          (record) => record.passFlag == '否' && record.rejectReason
        )

        if (allPassFlagsFalse) {
          const res = await this.$API.sendSampleCertification.plmSampleApplyConfirmData(
            this.dataInfo
          )
          if (res.code == 200 && res.data) {
            this.$toast({ content: this.$t('驳回成功'), type: 'success' })

            this.$router.push({
              name: 'send-sample-management',
              query: {
                timeStamp: new Date().getTime()
              }
            })
          }
        }
      }
    },

    /**
     * 处理删除
     */
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    },

    /**
     * 处理编辑完成
     */
    editComplete(args) {
      if (args.$event) {
        // 保存数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sc-table {
  :deep(.vxe-table) {
    .vxe-body--row {
      &.row--editing {
        background-color: #f5f7fa;
      }
    }
  }
}

.upload-label {
  cursor: pointer;
}

.upload-input {
  display: none;
}

.file-info {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  color: #2783fe;
  cursor: pointer;
}

.upload-label,
.reupload-label {
  position: relative;
  display: inline-block;
  color: #2783fe;
  cursor: pointer;
  font-size: 13px;
}

.upload-input {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}
</style>
