import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import dayjs from 'dayjs'

// 状态
export const statusOptions = [
  { value: 1, text: i18n.t('已发布') },
  { value: 2, text: i18n.t('待审阅') },
  { value: 3, text: i18n.t('已驳回') },
  { value: 4, text: i18n.t('已完成') }
]

// 按钮
export const toolbar = [
  // {
  //   code: 'confirm',
  //   name: i18n.t('确认'),
  //   status: 'info',
  //   loading: false
  // }
]

export const columnData = [
  // {
  //   width: 50,
  //   type: 'checkbox',
  //   fixed: 'left',
  //   align: 'center'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },

  {
    field: 'companyName',
    title: i18n.t('公司'),
    formatter: ({ row }) => {
      return (row?.companyCode ? row?.companyCode + '-' : '') + (row?.companyName || '')
    }
  },
  {
    field: 'productLine',
    title: i18n.t('产品线')
  },
  {
    field: 'applyNo',
    title: i18n.t('送样申请单号'),
    slots: { default: 'applyNo' }
  },
  {
    field: 'applyName',
    title: i18n.t('名称')
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    searchOptions: {
      ...MasterDataSelect.supplierNoScope
    }
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'promiseDeliveryDate',
    title: i18n.t('承诺送样日期'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'actualDeliveryDate',
    title: i18n.t('实际送样日期'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'authApplyNo',
    title: i18n.t('认证申请单号'),
    slots: { default: 'authApplyNo' }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
