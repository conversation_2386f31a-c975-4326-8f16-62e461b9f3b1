<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="configGroupType" :label="$t('配置方式')">
        <mt-select
          v-model="ruleForm.configGroupType"
          :data-source="configGroupTypeOptions"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="siteCode"
        :label="$t('工厂')"
        v-if="
          ruleForm.configGroupType === 1 ||
          ruleForm.configGroupType === 2 ||
          ruleForm.configGroupType === 3 ||
          ruleForm.configGroupType === 4
        "
      >
        <mt-select
          :open-dispatch-change="false"
          v-model="ruleForm.siteCode"
          :data-source="siteCodeOptions"
          :fields="{ text: 'label', value: 'siteCode' }"
          :placeholder="$t('请选择')"
          @change="siteCodeChange"
          :allow-filtering="true"
          :filtering="postSiteFuzzyQuery"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="itemCode" :label="$t('物料')" v-if="ruleForm.configGroupType === 4">
        <ItemCode
          @updateItemInfo="updateItemInfo"
          :item-code="ruleForm.itemCode"
          :item-name="ruleForm.itemName"
        ></ItemCode>
      </mt-form-item>
      <mt-form-item
        prop="siteAddressCode"
        :label="$t('库存地点')"
        v-if="
          ruleForm.configGroupType === 1 ||
          ruleForm.configGroupType === 3 ||
          ruleForm.configGroupType === 4
        "
      >
        <mt-select
          v-model="ruleForm.siteAddressCode"
          :data-source="siteAddressCodeOptions"
          :fields="{ text: 'label', value: 'locationCode' }"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          @open="startOpen"
          :filtering="serchText"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="subSiteName" :label="$t('分厂')" v-if="ruleForm.configGroupType === 3">
        <!-- <mt-select
          v-model="ruleForm.subSiteCode"
          :data-source="siteCodeOptions"
          :fields="{ text: 'label', value: 'siteCode' }"
          :placeholder="$t('请选择')"
          @change="siteCodeChange1"
        ></mt-select> -->
        <mt-input
          type="text"
          v-model="ruleForm.subSiteName"
          :placeholder="$t('请输入文本')"
          v-if="maxlength1"
          :maxlength="maxlength1"
        ></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="subSiteCode"
        :label="$t('分厂编码')"
        v-if="ruleForm.configGroupType === 3"
      >
        <mt-input
          type="text"
          v-model="ruleForm.subSiteCode"
          :placeholder="$t('请输入文本')"
          v-if="maxlength4"
          :maxlength="maxlength4"
        ></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="subSiteAddressName"
        :label="$t('分厂库存地点名称')"
        v-if="ruleForm.configGroupType === 3"
      >
        <!-- <mt-select
          v-model="ruleForm.subSiteAddressCode"
          :data-source="subSiteAddressCodeOptions"
          :fields="{ text: 'label', value: 'locationCode' }"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          :filtering="serchText1"
          @open="startOpen1"
          :open-dispatch-change="false"
        ></mt-select> -->
        <mt-input
          type="text"
          v-model="ruleForm.subSiteAddressName"
          :placeholder="$t('请输入文本')"
          v-if="maxlength1"
          :maxlength="maxlength1"
        ></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="subSiteAddressCode"
        :label="$t('分厂库存地点编码')"
        v-if="ruleForm.configGroupType === 3"
      >
        <mt-input
          type="text"
          v-model="ruleForm.subSiteAddressCode"
          :placeholder="$t('请输入文本')"
          v-if="maxlength4"
          :maxlength="maxlength4"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="supplierCode" :label="$t('加工商')" v-if="ruleForm.configGroupType === 2">
        <mt-select
          v-model="ruleForm.supplierCode"
          :data-source="supplierOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          :fields="{ text: 'label', value: 'supplierCode' }"
          :filtering="serchText2"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="consigneeName" :label="$t('送货联系人')">
        <mt-input
          type="text"
          v-model="ruleForm.consigneeName"
          :placeholder="$t('请输入文本')"
          v-if="maxlength3"
          :maxlength="maxlength3"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="consigneePhone" :label="$t('送货联系电话')">
        <mt-inputNumber
          :width="400"
          :show-spin-button="false"
          :min="1"
          v-model="ruleForm.consigneePhone"
          :placeholder="$t('请输入')"
        ></mt-inputNumber>
      </mt-form-item>
      <mt-form-item prop="consigneeAddress" :label="$t('送货地址')">
        <mt-input
          type="text"
          v-model="ruleForm.consigneeAddress"
          :placeholder="$t('请输入文本')"
          v-if="maxlength1"
          :maxlength="maxlength1"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="consigneeAddressCode" :label="$t('送货地址编码')">
        <mt-input
          type="text"
          v-model="ruleForm.consigneeAddressCode"
          :placeholder="$t('请输入文本')"
          v-if="maxlength4"
          :maxlength="maxlength4"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="needDefault" :label="$t('是否默认')">
        <mt-switch
          :active-value="1"
          :inactive-value="0"
          v-model="ruleForm.needDefault"
          :on-label="$t('是')"
          :off-label="$t('否')"
        ></mt-switch>
      </mt-form-item>
      <mt-form-item prop="relationItemExpend" :label="$t('关联物料消耗')">
        <mt-switch
          :active-value="1"
          :inactive-value="0"
          v-model="ruleForm.relationItemExpend"
          :on-label="$t('是')"
          :off-label="$t('否')"
        ></mt-switch>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { RegExpMap } from '@/utils/constant'
import ItemCode from './ItemCode.vue'
export default {
  components: {
    ItemCode
  },
  data() {
    const { phoneNumReg } = RegExpMap
    const driverPhoneValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入送货人手机号')))
      } else if (!phoneNumReg.test(value)) {
        callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        this.$refs.ruleForm.clearValidate(['consigneePhone'])
        callback()
      }
    }
    const itemCodeValid = (rule, value, callback) => {
      console.log(this.ruleForm.itemCode, '我是选择的物料')
      if (!this.ruleForm.itemCode) {
        callback(new Error(this.$t('请选择物料')))
      } else {
        this.$refs.ruleForm.clearValidate(['itemCode'])
        callback()
      }
    }
    return {
      maxlength1: 128,
      maxlength3: 40,
      maxlength4: 50,
      configGroupTypeOptions: [
        { text: this.$t('工厂+库存地点'), value: 1 },
        { text: this.$t('工厂+加工商'), value: 2 },
        { text: this.$t('工厂+库存地点+分厂+分厂库存地点'), value: 3 },
        { text: this.$t('工厂+物料+库存地点'), value: 4 },
        { text: this.$t('加工商'), value: 5 }
      ], //配置
      siteCodeOptions: [], // 工厂 分厂 下拉
      siteAddressCodeOptions: [], // 库存地点 分厂库存地点下拉
      subSiteAddressCodeOptions: [], //分厂库存地点
      supplierOptions: [], //加工商下拉
      dialogTitle: '',
      rules: {
        configGroupType: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        subSiteName: [{ required: true, message: this.$t('请输入分厂'), trigger: 'blur' }],
        subSiteCode: [
          {
            required: true,
            message: this.$t('请输入分厂编码'),
            trigger: 'blur'
          }
        ],
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        consigneeName: [
          {
            required: true,
            message: this.$t('请输入送货人姓名'),
            trigger: 'blur'
          }
        ],
        itemCode: [
          {
            required: true,
            trigger: 'change',
            validate: itemCodeValid,
            message: this.$t('请选择物料')
          }
        ],
        consigneePhone: [
          {
            required: true,
            trigger: 'blur',
            validator: driverPhoneValidator
          }
        ],
        consigneeAddress: [
          {
            required: true,
            message: this.$t('请输入送货人地址'),
            trigger: 'blur'
          }
        ],
        supplierCode: [{ required: true, message: this.$t('请选择加工商'), trigger: 'blur' }],
        subSiteAddressCode: [
          {
            required: true,
            message: this.$t('请输入分厂库存地点编码'),
            trigger: 'blur'
          }
        ],
        subSiteAddressName: [
          {
            required: true,
            message: this.$t('请输入分厂库存地点名称'),
            trigger: 'blur'
          }
        ],
        siteAddressCode: [
          {
            required: true,
            message: this.$t('请选择库存地点'),
            trigger: 'blur'
          }
        ],
        needDefault: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        relationItemExpend: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        consigneeAddressCode: [
          {
            required: true,
            message: this.$t('请输入送货人地址编码'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        id: '',
        configGroupType: '', //配置组合方式
        siteCode: '', //工厂code
        status: 1,
        itemCode: '',
        itemName: '',
        subSiteCode: '', //分厂code
        subSiteName: '',
        subSiteAddressName: '',
        siteAddressCode: '', //库存地点code
        subSiteAddressCode: '', //分厂库存地点code
        supplierCode: '', //供应商code
        consigneeName: '', //送货人姓名
        consigneePhone: '', //送货人电话
        consigneeAddress: '', //送货人地址
        consigneeAddressCode: '', //送货地址编码
        needDefault: 0, //是否默认
        relationItemExpend: 0 //关联物料消耗
      },
      show: false,
      entryInfo: {}
    }
  },
  mounted() {
    this.getOptions()
    this.getLocation = utils.debounce(this.getLocation, 1000)
    this.postSiteFuzzyQuery = utils.debounce(this.postSiteFuzzyQuery, 1000)
    this.getSupplier = utils.debounce(this.getSupplier, 1000)
  },
  methods: {
    updateItemInfo(val) {
      console.log(val, '我是徐福街道三')
      this.ruleForm.itemCode = val.itemCode
      this.ruleForm.itemName = val.itemName
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args, entryFirst) {
      const { text, updateData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeOptions = list.map((item) => {
              return {
                ...item,
                name: item.siteName,
                label: `${item.siteCode}-${item.siteName}`
              }
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteCodeOptions)
              })
            }
            if (entryFirst === '1') {
              this.ruleForm.siteCode = this.entryInfo.row.siteCode
            }
          }
        })
        .catch(() => {})
    },
    siteCodeChange1(val) {
      console.log('分厂改变', val)
      if (val.itemData?.siteCode) {
        // this.getLocation1("", val.itemData.siteCode);
      }
    },
    siteCodeChange(val) {
      console.log('工厂改变', val)
      if (val.itemData?.siteCode) {
        this.getLocation('', val.itemData.siteCode)
      }
    },
    startOpen() {
      if (!this.siteAddressCodeOptions.length) {
        this.getLocation()
      }
    },
    startOpen1() {
      if (!this.subSiteAddressCodeOptions.length) {
        this.getLocation1()
      }
    },
    async serchText(val, type) {
      console.log('库存搜索值', val, type)
      await this.getLocation(val && val.text ? val.text : '', null, val)
    },
    serchText1(val, type) {
      console.log('分厂库存搜索值', val, type)
      // this.getLocation1(val && val.text ? val.text : "");
    },
    // getLocation1(val, subSiteCode) {
    //   let str = val || this.ruleForm.subSiteAddressCode;
    //   let params = {
    //     commonCode: subSiteCode || this.ruleForm.subSiteCode, //工厂code
    //     dataLimit: 50,
    //     fuzzyParam: str || "", //搜索参数
    //   };
    //   if (!params.commonCode) {
    //     console.log("工厂没有");
    //     return;
    //   }
    //   this.$API.masterData.getLocationFuzzyQuery(params).then((res) => {
    //     let list = res.data || [];
    //     list.forEach((item) => {
    //       if (item.externalCode) {
    //         item.locationCode = item.externalCode;
    //         if (item.externalName) {
    //           item.locationName = item.externalName;
    //         }
    //       }
    //       item.label = `${item.locationCode}-${item.locationName}`;
    //     });
    //     this.subSiteAddressCodeOptions = list;
    //   });
    // },
    async getLocation(val, siteCode, e, entryFirst) {
      //查询库存地点
      console.log(e, '我是库存地点查询参数')
      let str = val || this.ruleForm.siteAddressCode
      const currentSiteCode = siteCode || this.ruleForm.siteCode

      if (!currentSiteCode) {
        console.log('工厂没有')
        return
      }

      // 当工厂编码为2547时，使用queryPvWarehouseList接口
      if (currentSiteCode === '2547') {
        const params = {
          page: {
            current: 1,
            size: 50
          },
          fuzzyParam: str || '' //搜索参数
        }

        await this.$API.masterData.queryPvWarehouseList(params).then((res) => {
          let list = res.data?.records || []
          list.forEach((item) => {
            // 将光伏仓库数据格式转换为与库存地点一致的格式
            item.locationCode = item.warehouseCode
            item.locationName = item.warehouseName
            item.label = `${item.warehouseCode}-${item.warehouseName}`
          })
          this.siteAddressCodeOptions = list
          if (e && e.updateData) {
            this.$nextTick(() => {
              e.updateData(this.siteAddressCodeOptions)
            })
          }
          if (entryFirst === '1') {
            this.ruleForm.siteAddressCode = this.entryInfo.row.siteAddressCode
          }
        })
      } else {
        // 其他工厂编码使用原有的getLocationFuzzyQuery接口
        const params = {
          commonCode: currentSiteCode, //工厂code
          dataLimit: 50,
          fuzzyParam: str || '' //搜索参数
        }

        await this.$API.masterData.getLocationFuzzyQuery(params).then((res) => {
          let list = res.data || []
          list.forEach((item) => {
            if (item.externalCode) {
              item.locationCode = item.externalCode
              if (item.externalName) {
                item.locationName = item.externalName
              }
            }
            item.label = `${item.locationCode}-${item.locationName}`
          })
          this.siteAddressCodeOptions = list
          if (e && e.updateData) {
            this.$nextTick(() => {
              e.updateData(this.siteAddressCodeOptions)
            })
          }
          if (entryFirst === '1') {
            this.ruleForm.siteAddressCode = this.entryInfo.row.siteAddressCode
          }
        })
      }
    },
    getOptions() {
      // //工厂
      // this.$API.masterData.findOrgSiteInfo({}).then((res) => {
      //   let list = res.data || [];
      //   list.forEach((item) => {
      //     item.label = `${item.siteCode}-${item.siteName}`;
      //   });
      //   this.siteCodeOptions = res.data || [];
      // });
      // //供应商下拉
      // this.$API.masterData.getSupplier().then((res) => {
      //   this.supplierOptions = res.data || [];
      // });
    },
    serchText2(val) {
      console.log('搜索值', val)
      this.getSupplier(val && val.text ? val.text : '')
    },
    getSupplier(val, entryFirst) {
      //查询供应商的数据
      let str = val || this.ruleForm.supplierCode
      let params = {
        fuzzyNameOrCode: str || ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.supplierCode}-${item.supplierName}`
        })
        this.supplierOptions = res.data || []
        if (entryFirst === '1') {
          this.ruleForm.supplierCode = this.entryInfo.row.supplierCode
        }
      })
    },
    // 初始化
    async dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('新增')) {
        this.ruleForm = {
          configGroupType: '', //配置组合方式
          siteCode: '', //工厂code
          status: 1,
          subSiteCode: '', //分厂code
          subSiteName: '',
          subSiteAddressName: '',
          siteAddressCode: '', //库存地点code
          subSiteAddressCode: '', //分厂库存地点code
          supplierCode: null, //供应商code
          consigneeName: '', //送货人姓名
          consigneePhone: '', //送货人电话
          consigneeAddress: '', //送货人地址
          needDefault: 0, //是否默认
          relationItemExpend: 0, //关联物料消耗
          consigneeAddressCode: '',
          itemCode: '',
          itemName: ''
        }
        this.postSiteFuzzyQuery({ text: undefined })
        this.getSupplier()
      }
      if (this.dialogTitle === this.$t('编辑')) {
        this.entryInfo = entryInfo
        console.log(entryInfo.row.supplierCode, '编辑数据')
        this.ruleForm.status = entryInfo.row.status
        this.ruleForm.status = entryInfo.row.status
        this.ruleForm.id = entryInfo.row.id
        this.ruleForm.configGroupType = entryInfo.row.configGroupType //配置组合方式
        this.ruleForm.siteCode = null //工厂code
        this.ruleForm.subSiteCode = entryInfo.row.subSiteCode //分厂code
        this.ruleForm.subSiteName = entryInfo.row.subSiteName
        this.ruleForm.subSiteAddressName = entryInfo.row.subSiteAddressName
        // this.ruleForm.siteAddressCode = entryInfo.row.siteAddressCode; //库存地点code
        this.ruleForm.subSiteAddressCode = entryInfo.row.subSiteAddressCode //分厂库存地点code
        this.ruleForm.supplierCode = null //供应商code
        this.ruleForm.supplierName = null //供应商code
        this.ruleForm.consigneeName = entryInfo.row.consigneeName //送货人姓名
        this.ruleForm.consigneePhone = entryInfo.row.consigneePhone //送货人电话
        this.ruleForm.consigneeAddress = entryInfo.row.consigneeAddress //送货人地址
        this.ruleForm.needDefault = entryInfo.row.needDefault //是否默认
        this.ruleForm.consigneeAddressCode = entryInfo.row.consigneeAddressCode
        this.ruleForm.relationItemExpend = entryInfo.row.relationItemExpend //关联物料消耗
        this.ruleForm.itemCode = entryInfo.row.itemCode
        this.ruleForm.itemName = entryInfo.row.itemName
        // await this.getLocation1();
        this.getSupplier(entryInfo.row.supplierCode, '1')
        this.postSiteFuzzyQuery({ text: entryInfo.row.siteCode }, '1')
        this.getLocation(entryInfo.row.siteAddressCode, entryInfo.row.siteCode, '', '1')
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            configGroupType: this.ruleForm.configGroupType,
            consigneeAddress: this.ruleForm.consigneeAddress,
            consigneeName: this.ruleForm.consigneeName,
            consigneePhone: this.ruleForm.consigneePhone,
            needDefault: this.ruleForm.needDefault,
            relationItemExpend: this.ruleForm.relationItemExpend,
            siteAddress: this.ruleForm.siteAddressCode,
            siteAddressName: this.siteAddressCodeOptions.find((item) => {
              return item.locationCode === this.ruleForm.siteAddressCode
            })?.locationName,
            siteCode: this.ruleForm.siteCode,
            siteId: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.id,
            siteName: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.siteName,
            status: this.ruleForm.status,
            // subSiteAddressName: this.subSiteAddressCodeOptions.find((item) => {
            //   return item.locationCode === this.ruleForm.subSiteAddressCode;
            // })?.locationName,
            subSiteAddressName: this.ruleForm.subSiteAddressName,
            subSiteAddress: this.ruleForm.subSiteAddressCode,
            subSiteCode: this.ruleForm.subSiteCode,
            // subSiteName: this.siteCodeOptions.find((item) => {
            //   return item.siteCode === this.ruleForm.subSiteCode;
            // })?.siteName,
            subSiteName: this.ruleForm.subSiteName,
            supplierCode: this.ruleForm.supplierCode,
            supplierId: this.supplierOptions.find((item) => {
              return item.supplierCode === this.ruleForm.supplierCode
            })?.id,
            supplierName: this.supplierOptions.find((item) => {
              return item.supplierCode === this.ruleForm.supplierCode
            })?.supplierName,
            consigneeAddressCode: this.ruleForm.consigneeAddressCode,
            itemCode: this.ruleForm.itemCode,
            itemName: this.ruleForm.itemName
          }
          if (params.configGroupType === 1) {
            params.subSiteAddressName = ''
            params.subSiteAddress = ''
            params.subSiteCode = ''
            params.subSiteName = ''
            params.supplierCode = ''
            params.supplierId = ''
            params.supplierName = ''
            params.itemCode = ''
            params.itemName = ''
          }
          if (params.configGroupType === 2) {
            params.subSiteAddressName = ''
            params.subSiteAddress = ''
            params.siteAddress = ''
            params.subSiteCode = ''
            params.subSiteName = ''
            params.itemCode = ''
            params.itemName = ''
          }
          if (params.configGroupType === 3) {
            params.supplierCode = ''
            params.supplierId = ''
            params.supplierName = ''
            params.itemCode = ''
            params.itemName = ''
          }
          if (params.configGroupType === 4) {
            params.subSiteAddressName = ''
            params.subSiteAddress = ''
            params.subSiteCode = ''
            params.subSiteName = ''
            params.supplierCode = ''
            params.supplierId = ''
            params.supplierName = ''
          }
          if (params.configGroupType === 5) {
            params.subSiteAddressName = ''
            params.subSiteAddress = ''
            params.siteAddress = ''
            params.subSiteCode = ''
            params.subSiteName = ''
            params.itemCode = ''
            params.itemName = ''
            params.siteCode = ''
          }
          if (this.dialogTitle === this.$t('编辑')) {
            params.id = this.ruleForm.id
            this.$API.deliverySchedule.pickupOrderAddressExtendupdate(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
          if (this.dialogTitle === this.$t('新增')) {
            this.$API.deliverySchedule.pickupOrderAddressExtendsave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
