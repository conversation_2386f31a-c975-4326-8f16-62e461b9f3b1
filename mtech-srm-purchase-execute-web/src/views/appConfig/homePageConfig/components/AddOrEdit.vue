<!-- 采方-首页配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :height="800"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="announcementTitle" :label="$t('公告标题')">
            <mt-input
              v-model="formData.announcementTitle"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="announcementSummary" :label="$t('公告概要')">
            <mt-input
              v-model="formData.announcementSummary"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="24">
          <mt-form-item prop="announcementDetails" :label="$t('公告详情')">
            <mt-input
              v-model="formData.announcementDetails"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="announceShowEndTime" :label="$t('首页公告显示截止时间')">
            <mt-date-picker
              v-model="formData.announceShowEndTime"
              :show-clear-button="true"
              :allow-edit="false"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="versionNumber" :label="$t('版本号')">
            <mt-input
              v-model="formData.versionNumber"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
              @blur="versionNumberBlur"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="versionDesc" :label="$t('版本说明')">
            <mt-input
              v-model="formData.versionDesc"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="installPackage" :label="$t('安卓安装包')">
            <mt-button size="small" @click="handleUpload('fileRef')">{{
              $t('选择文件')
            }}</mt-button>
            <input
              type="file"
              ref="fileRef"
              style="display: none"
              @change="(data) => chooseFiles(data, 'android')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="downloadPath" :label="$t('下载地址')">
            <mt-input
              v-if="!formData.downloadPath"
              v-model="formData.downloadPath"
              :show-clear-button="true"
              :disabled="true"
              :placeholder="$t('请选择安卓安装包')"
            />
            <div v-else style="border: 1px solid #f1f1f1; padding: 4px; word-break: break-all">
              {{ formData.downloadPath }}
            </div>
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="installPackage" :label="$t('iOS安装包')">
            <mt-button size="small" @click="handleUpload('fileRefIos')">{{
              $t('选择文件')
            }}</mt-button>
            <input
              type="file"
              ref="fileRefIos"
              style="display: none"
              @change="(data) => chooseFiles(data, 'ios')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="appleDownloadPath" :label="$t('下载地址')">
            <mt-input
              v-if="!formData.appleDownloadPath"
              v-model="formData.appleDownloadPath"
              :show-clear-button="true"
              :disabled="true"
              :placeholder="$t('请选择iOS安装包')"
            />
            <div v-else style="border: 1px solid #f1f1f1; padding: 4px; word-break: break-all">
              {{ formData.appleDownloadPath }}
            </div>
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col>
          <mt-form-item prop="carouselImage" :label="$t('轮播图')">
            <mt-button size="small" @click="handleUpload('picRef')">{{ $t('选择图片') }}</mt-button>
            <input
              type="file"
              ref="picRef"
              style="display: none"
              accept=".png, .jpg"
              @change="choosePic"
            />
            <div class="file-list">
              <div class="file-item" v-for="(item, index) in formData.picList" :key="index">
                <a @click="handlePreview(item)">
                  {{ item.fileName }}
                  <i class="vxe-icon-delete" @click.stop="handleDelete('picList', item)" />
                  <!-- <i class="vxe-icon-download" @click.stop="handleDownload(item)" /> -->
                </a>
              </div>
            </div>
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script>
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import { download } from '@/utils/utils'

export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        picList: []
      },
      rules: {
        announcementTitle: [
          {
            required: true,
            message: this.$t('请输入公告标题'),
            trigger: 'blur'
          }
        ],
        announcementSummary: [
          {
            required: true,
            message: this.$t('请输入公告概要'),
            trigger: 'blur'
          }
        ],
        announcementDetails: [
          {
            required: true,
            message: this.$t('请输入公告详情'),
            trigger: 'blur'
          }
        ],
        announceShowEndTime: [
          {
            required: true,
            message: this.$t('请选择首页公告显示截止时间'),
            trigger: 'blur'
          }
        ],
        versionNumber: [
          {
            required: true,
            message: this.$t('请输入版本号'),
            trigger: 'blur'
          }
        ],
        versionDesc: [
          {
            required: true,
            message: this.$t('请输入版本说明'),
            trigger: 'blur'
          }
        ],
        downloadPath: [
          {
            required: true,
            message: this.$t('请选择安装包'),
            trigger: 'blur'
          }
        ],
        carouselImage: [
          {
            required: true,
            message: this.$t('请选择轮播图'),
            trigger: 'blur'
          }
        ]
      },
      isVersionNumber: false
    }
  },
  methods: {
    versionNumberBlur() {
      this.isVersionNumber = this.validVersionNumber(this.formData.versionNumber)
      if (!this.isVersionNumber) {
        this.formData.versionNumber = ''
      }
    },
    validVersionNumber(version) {
      const regex = /^\d+(?:\.\d+){1,2}$/
      return regex.test(version)
    },
    handleUpload(flag) {
      this.$refs[flag].click()
    },
    chooseFiles(data, type) {
      const fileList = Object.values(data?.target?.files)
      if (fileList.length < 1) {
        return
      }
      if (fileList.length > 5) {
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      const params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      // 判断文件格式
      const typeList = type === 'ios' ? ['ipa'] : ['apk']
      let isNotAcceptType = fileList.some((item) => {
        let _tempInfo = item.name.split('.')
        return _tempInfo.length < 2 || !typeList.includes(_tempInfo[_tempInfo.length - 1])
      })
      if (isNotAcceptType) {
        this.$toast({
          content: this.$t('文件格式仅支持') + type === 'ios' ? 'ipa' : 'apk',
          type: 'warning'
        })
        return
      }
      // 判断当个文件是否过大
      let isOutOfRange = false
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({ content: params.msg })
        return
      }
      fileList.forEach((item) => {
        const _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1)
        this.uploadFile(_data, 'installPackage', type)
      })
    },
    async uploadFile(fileData, key, type) {
      this.$loading()
      const res = await this.$API.fileService.uploadPublicFile(fileData)
      this.$hloading()
      if (res.code === 200) {
        if (key === 'installPackage') {
          this.$set(
            this.formData,
            type === 'ios' ? 'appleDownloadPath' : 'downloadPath',
            res.data.remoteUrl
          )
          this.$set(this.formData, type === 'ios' ? 'appleSize' : 'size', res.data.fileSize)
        }
        if (key === 'picList') {
          const dataList = [
            ...this.formData[key],
            {
              ...res.data,
              fileId: res.data?.id
            }
          ]
          this.$set(this.formData, key, dataList)
          this.formData.carouselImage = this.formData[key].length !== 0 ? true : null
        }
      }
    },
    // 预览
    handlePreview(file) {
      let params = {
        id: file?.id,
        useType: 2
      }
      this.$API.fileService.getMtPreviewPub(params).then((res) => {
        window.open(res.data)
      })
    },
    // 删除
    handleDelete(key, file) {
      this.formData[key] = this.formData[key].filter((item) => item.id !== file.id)
      if (key === 'picList') {
        this.formData.carouselImage = this.formData[key].length !== 0 ? true : null
      }
    },
    // 下载
    handleDownload(file) {
      this.$API.fileService
        .downloadPublicFile({
          id: file.id
        })
        .then((res) => {
          download({
            fileName: file.fileName,
            blob: res.data
          })
        })
    },
    choosePic(data) {
      const fileList = Object.values(data?.target?.files)
      if (fileList.length < 1) {
        return
      }
      if (fileList.length > 5) {
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      const params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      // 判断文件格式
      let isNotAcceptType = fileList.some((item) => {
        let _tempInfo = item.name.split('.')
        return _tempInfo.length < 2 || !['png', 'jpg'].includes(_tempInfo[_tempInfo.length - 1])
      })
      if (isNotAcceptType) {
        this.$toast({
          content: this.$t('文件格式仅支持png/jpg'),
          type: 'warning'
        })
        return
      }
      // 判断当个文件是否过大
      let isOutOfRange = false
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({ content: params.msg })
        return
      }
      fileList.forEach((item) => {
        const _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1)
        this.uploadFile(_data, 'picList')
      })
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = cloneDeep(row)
        this.formData.announceShowEndTime = dayjs(Number(row.announceShowEndTime)).format(
          'YYYY-MM-DD'
        )
        this.$set(this.formData, 'picList', JSON.parse(row.bannerImageConfig))
        this.formData.carouselImage = this.formData.picList.length !== 0 ? true : null
      }
    },
    beforeOpen() {
      this.formData = {
        picList: []
      }
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {
        picList: []
      }
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {
        picList: []
      }
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = this.getParams()
      const api = this.$API.appConfig.saveHomePageConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    getParams() {
      let announce = JSON.stringify({
        announcementTitle: this.formData.announcementTitle,
        announcementSummary: this.formData.announcementSummary,
        announcementDetails: this.formData.announcementDetails
      })
      let params = {
        id: this.formData.id || null,
        announce,
        announceShowEndTime: dayjs(this.formData.announceShowEndTime).valueOf(),
        versionNumber: this.formData.versionNumber,
        versionDesc: this.formData.versionDesc,
        bannerImageConfig: JSON.stringify(this.formData.picList),
        size: this.formData.size,
        downloadPath: this.formData.downloadPath,
        appleSize: this.formData.appleSize,
        appleDownloadPath: this.formData.appleDownloadPath
      }
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.file-list {
  margin: auto 10px;
  .file-item {
    padding: 2px 0;
    a {
      color: #409eff;
      i {
        margin-left: 5px;
        font-weight: bold;
      }
    }
  }
}
</style>
