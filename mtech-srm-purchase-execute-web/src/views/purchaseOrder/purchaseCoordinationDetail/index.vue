<!-- eslint-disable prettier/prettier -->
<template>
  <div class="detail-fix-wrap full-height">
    <top-info
      :entry-id="entryId"
      :entry-type="entryType"
      :entry-draft="entryDraft"
      :top-info="topInfo"
      :cost-switch="costSwitch"
      :entry-source="entrySource"
      :acceptance-use="acceptanceUse"
      :from-path="fromPath"
      @updateCompanyLinkData="updateCompanyLinkData"
      @updatePurtenantLinkData="updatePurtenantLinkData"
      @save="save"
      @submit="submit"
      @publish="publish"
      @businessTypeNameChange="businessTypeNameChange"
      @queryRequestModelPo="queryRequestModelPo"
      @expeditedAllDetail="expeditedAllDetail"
      @cancleExpeditedAllDetail="cancleExpeditedAllDetail"
      ref="topInfo"
    ></top-info>
    <div class="bottom-tables">
      <mt-tabs
        tab-id="purchase-tab"
        :e-tab="false"
        id="poDetailTab"
        :data-source="componentConfig"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>

      <div class="bottom-box">
        <dataGrid
          :total-records-count="totalRecordsCount"
          :entry-draft="entryDraft"
          :top-info="topInfo"
          :company-id="companyId"
          :business-id="businessId"
          :module-key="moduleKey"
          :entry-source="entrySource"
          :entry-id="entryId"
          :entry-type="entryType"
          :fields="fields"
          :cost-switch="costSwitch"
          :entry-file-list="entryFileList"
          :entry-order-detail-list="entryOrderDetailList"
          :top-info-is-saved="topInfoIsSaved"
          ref="dataGrid"
          @updateOrderInfo="updateOrderInfo"
          @updateGrid="updateGrid"
          @updateEntryFileList="updateEntryFileList"
          @checkTopInfo="checkTopInfo"
          @updateShowDataGrid="updateShowDataGrid"
          @updateTopInfoUrgentime="updateTopInfoUrgentime"
          @updateTopInfoUrgentime1="updateTopInfoUrgentime1"
          @getModuleData1="updateGetModuleData1"
          v-show="currentInfo.moduleType == 6"
        ></dataGrid>
        <relative-file
          :entry-id="entryId"
          :entry-type="entryType"
          :module-file-list="moduleFileList"
          :entry-file-list="entryFileList"
          :new-file-list="newFileList"
          @updateFile="updateFile"
          change-node-code="po_item_file"
          v-show="currentInfo.moduleType == 7"
        ></relative-file>
        <cost-share
          :entry-id="entryId"
          :entry-draft="entryDraft"
          :entry-cost-switch="costSwitch"
          :entry-source="entrySource"
          @updateCosts="updateCosts"
          :entry-type="entryType"
          :entry-costs="entryCosts"
          ref="costShareRef"
          v-show="currentInfo.moduleType == 8"
        ></cost-share>
        <acceptance-plan
          :entry-draft="entryDraft"
          :entry-id="entryId"
          :top-info="topInfo"
          :fields="fields"
          :top-info-is-saved="topInfoIsSaved"
          :entry-acceptance-plan-list="entryAcceptancePlanList"
          :entry-acceptance-use="acceptanceUse"
          :entry-source="entrySource"
          :entry-type="entryType"
          :show-data-grid="showDataGrid"
          ref="acceptancePlan"
          v-show="currentInfo.moduleType == 15"
          @checkTopInfo="checkTopInfo"
          @updateAcceptancePlan="updateAcceptancePlan"
        >
        </acceptance-plan>
        <operationLog
          ref="operationLog"
          :entry-id="entryId"
          v-if="currentInfo.moduleType == 16"
        ></operationLog>
        <partners
          v-show="currentInfo.moduleType == 17"
          ref="partners"
          :entry-id="entryId"
          :entry-partners="entryPartners"
          :top-info-is-saved="topInfoIsSaved"
          :entry-type="entryType"
          @checkTopInfo="checkTopInfo"
          @updatePartners="updatePartners"
        ></partners>
      </div>
    </div>
  </div>
</template>
<!-- eslint-disable prettier/prettier -->
<script>
import bigDecimal from 'js-big-decimal'
import { cloneDeep } from 'lodash'
export default {
  components: {
    TopInfo: () => import('./components/topInfo'),
    RelativeFile: () => import('@/components/businessComponents/relativeFile1/index.vue'),
    dataGrid: () => import('./components/dataGrid'),
    costShare: () => import('./components/costShare'),
    acceptancePlan: () => import('./components/newAcceptancePlan'),
    operationLog: () => import('./components/operationLog'),
    partners: () => import('./components/partners')
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // console.log("我是vmaa阿", vm.pathUrl, from.path);
      console.log('我是vmaa阿', vm.pathUrl, from.path)
      vm.fromPath = from.path || from.fullPath || vm.pathUrl
    })
  },
  data() {
    return {
      fromPath: '',
      totalRecordsCount: '0',
      entryId: '', //编辑带入的订单id
      entryType: null, //1是新增 2是编辑 3是反馈异常
      entrySource: '', // 0采购申请 1手工创建 2商城进入 4合同进入
      entryDraft: '', //1是草稿 2不是草稿 草稿可以修改
      topInfo: {
        orderRel: '',
        urgentTime1: '',
        employeeId: '',
        buyerUser: '',
        buyerUserId: '',
        buyerUserName: '',
        currency: '',
        businessId: '', //业务类型id用来查询下载模板和上传模板数据用
        company: '',
        buyerOrg: '',
        taxTotal: '',
        freeTotal: '',
        requiredDeliveryDate: '',
        orderCode: '',
        purtenant: ''
      },
      componentConfig: [],
      fields: [], //订单明细表头
      moduleKey: '', //订单明细模块key
      moduleFileList: [], //相关附件用到的tree信息
      fileList: [], //所有的上传的文件
      gridList: [], //暂时认为所有的订单明细
      costsList: [], //所有的整单分摊信息
      costSwitch: '1', //是否开启整单分摊开启0 明细分摊1
      currentInfo: {},
      entryOrderDetailList: [], //编辑带入的订单明细
      draftOrderDetailList: [], //编辑草稿状态下带入的订单明细或者商城过来带入的订单明细
      entryFileList: [], //编辑带入的附件
      entryCosts: [], //编辑带入的整单分摊信息
      entryInfo: {},
      newFileList: [], //改变后的最新的明细行文件数据
      acceptanceUse: '0', //是否启用验收计划
      entryAcceptancePlanList: [], //带入的验收计划列表数据
      topInfoIsSaved: '0', //头部信息是否填好
      allIsPercentage: false,
      businessId: '', //业务类型id
      companyId: '', //公司id
      showDataGrid: [], //验收计划展示的最新的订单明细数据
      validatePercentage: false,
      validateAcceptanceType: false,
      validateAcceptor: false, //校验验收人是否填写
      validateacceptanceQuantity: false,
      validatefreeTotal: false,
      validatetaxTotal: false,
      partners: [], //合作方数据
      entryPartners: [], //带入的合作方数据
      allValidatePartner: false, //校验合作方数据是否都正确
      addId: '1', //合同进入用
      contractId: null, //合同id
      contractCode: null, //合同编码
      isSourceContract: false // 来源合同
    }
  },
  mounted() {
    this.entryType = this.$route.query.type
    this.entrySource = this.$route.query.source
    this.entryDraft = this.$route.query.draft
    this.init()
  },
  destroyed() {
    sessionStorage.removeItem('supplierId')
  },
  methods: {
    updatePartners(val, allValidate) {
      this.partners = val
      this.allValidatePartner = allValidate
    },
    updateShowDataGrid(val) {
      //订单明细有改动 ,更新展示的最新的订单明细
      console.log('updateShowDataGrid', val)
      this.showDataGrid = val
    },
    updatePurtenantLinkData(val) {
      //更新订单明细和供应商关联的数据
      this.$refs.dataGrid.updatePurtenantLinkData(val)
    },
    updateCompanyLinkData() {
      //更新订单明细和公司关联的数据
      this.$refs.dataGrid.updateCompanyLinkData()
    },
    async checkTopInfo() {
      //校验头部信息是否填完
      let flag = await this.$refs.topInfo.checkTopInfo()
      this.topInfoIsSaved = flag
    },
    updateAcceptancePlan(
      acceptanceUse,
      objInfo,
      allIsPercentage,
      validatePercentage,
      validateAcceptanceType,
      validateAcceptor,
      validateacceptanceQuantity,
      validatefreeTotal,
      validatetaxTotal
    ) {
      //更新验收计划的数据,启用禁用和列表
      this.acceptanceUse = acceptanceUse
      this.allIsPercentage = allIsPercentage
      this.validatePercentage = validatePercentage
      this.validateAcceptanceType = validateAcceptanceType
      this.validateAcceptor = validateAcceptor
      this.validateacceptanceQuantity = validateacceptanceQuantity
      this.validatefreeTotal = validatefreeTotal
      this.validatetaxTotal = validatetaxTotal
      if (this.acceptanceUse === '1') {
        //如果启用了验收计划 ,把验收计划数据放到订单明细中
        this.gridList.forEach((item) => {
          Object.keys(objInfo).some((item1) => {
            if (item1.substring(3) == item.addId) {
              item.maxAcceptanceItemNo = objInfo[item1][0]?.maxAcceptanceItemNo || 0
              item.acceptances = objInfo[item1]
              return
            }
          })
        })
        console.log(this.gridList, this.validatePercentage, '要发宋的数据')
      }
      if (this.acceptanceUse === '0') {
        this.gridList.forEach((item) => {
          item.acceptances = []
        })
      }
    },
    setBuyerUser() {
      let userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
      this.topInfo.buyerUser = userInfo.employeeCode
      this.topInfo.employeeId = userInfo.employeeId
    },
    async init() {
      // 5种情况 采购申请进入新建 采购申请进入编辑 新建进入 新建编辑 详情
      if (this.entryType === '1') {
        if (this.entrySource == '0') {
          //采购申请进入新建
          let entryInfo = JSON.parse(sessionStorage.getItem('purchaseRequestSession'))
          this.entryInfo = entryInfo
          this.topInfo.businessTypeName = entryInfo.businessInfo.name
          this.topInfo.businessTypeCode = entryInfo.businessInfo.code
          this.topInfo.businessId = entryInfo.businessInfo.id
          this.topInfo.companyInfo = {
            companyId: entryInfo.companyInfo.id,
            companyName: entryInfo.companyInfo.name,
            companyCode: entryInfo.companyInfo.code
          }
          this.topInfo.company = entryInfo.companyInfo.id
          this.topInfo.companyName1 = `${entryInfo.companyInfo.code}-${entryInfo.companyInfo.name}`
          this.setBuyerUser()
          this.getPebusinessConfig()
          this.setSupplierAndBusinessInfo(
            '',
            '',
            entryInfo.businessInfo.code,
            entryInfo.businessInfo.id,
            ''
          )
        }
        if (this.entrySource == '1') {
          //新建进入
          // this.topInfo.businessTypeName = this.$route.query.label;
          // this.topInfo.businessTypeCode = this.$route.query.code;
          // this.topInfo.businessId = this.$route.query.id;
          this.setBuyerUser()
        }
        if (this.entrySource === '2') {
          //从商城进入
          this.setBuyerUser()
        }
        if (this.entrySource === '4') {
          //从合同进入
          let key = this.$route.query.key
          this.getOrderKey(key)
          this.setBuyerUser()
        }
      }
      if (this.entryType === '2' || this.entryType === '3') {
        this.entryId = this.$route.query.orderid
        await this.getPurOrderModuleConfig()
        await this.getTenantPurOrder()
        await this.getFileNodeByDocId()
        await this.getPurOrderModuleData2()
        this.getPurOrderModuleData()
      }
    },
    async queryRequestModelPo() {
      //从商城进入根据key查询信息
      let key = this.$route.query.key
      let params = {
        businessTypeCode: this.topInfo.businessTypeCode,
        showKey: key
      }
      let entryList = []
      await this.$API.purchaseOrder.queryRequestModelPo(params).then((res) => {
        // res.data.supplierId = "1460896518313013249";
        // res.data.supplierName = "光伏电子股份有限公司";
        // res.data.supplierCode = "SUPPLIER_CODE2203221551305919";
        // res.data.requestItemRequestList[0].skuCode = "***********";
        // res.data.companyId = "1399202858620522497";
        this.topInfo = {
          //供应商
          orderRel: '',
          urgentTime1: '',
          employeeId: this.topInfo.employeeId,
          buyerUser: this.topInfo.buyerUser,
          // purtenant: res.data.supplierCode || "APPLE",
          purtenant: res.data.supplierCode,
          buyerGroup: '',
          businessTypeName: res.data.businessTypeName,
          businessTypeCode: res.data.businessTypeCode,
          businessId: res.data.businessTypeId,
          buyerOrg: '',
          taxTotal: '',
          freeTotal: '',
          requiredDeliveryDate: '',
          orderCode: '',
          company: res.data?.companyId,
          companyInfo: {
            companyId: res.data.companyId,
            companyName: res.data.companyName,
            companyCode: res.data.companyCode
          },
          companyName1: `${res.data.companyCode}-${res.data.companyName}`,
          currency: ''
        }
        this.setSupplierAndBusinessInfo(
          res.data.supplierId,
          res.data.supplierCode,
          res.data.businessTypeCode,
          res.data.businessTypeId,
          ''
        )
        this.$store.commit('updateCompanyId', res.data.companyId)
        this.$store.commit('updateCompanyCode', res.data.companyCode)
        entryList = res.data.requestItemRequestList
      })
      await this.getPebusinessConfig()
      this.entryOrderDetailList = entryList
      this.draftOrderDetailList = cloneDeep(entryList)
    },
    async getOrderKey(key) {
      //从合同进入根据key查询合同列表,根据合同里面的申请id获取 采购申请行数据
      let params = key
      let dataSource = []
      await this.$API.contract.getOrderKey(params).then((res) => {
        if (res.data.length) {
          dataSource = res.data
          this.contractId = res.data[0]?.contractId
          this.contractCode = res.data[0]?.contractCode

          // this.topInfo = {
          //   //供应商，公司，采购组 ,业务类型
          //   purtenant: "SHYL20220107105044", //供应商
          //   buyerGroup: "CGYZ20210907135855", //采购组
          //   // buyerOrgId: res.data[0].purGroupId || "1435121956868947969",
          //   // buyerOrgName: res.data[0].purGroupName || "采购二组",
          //   businessTypeName: "生产采购", //业务类型
          //   businessTypeCode: "BT002",
          //   businessId: "1456500223161524226",
          //   buyerOrg: "", //采购组织
          //   taxTotal: "",
          //   freeTotal: "",
          //   requiredDeliveryDate: "",
          //   orderCode: "",
          //   companyInfo: {
          //     companyId: "1475802394828308482",
          //     companyName: "电解智科技有限公司",
          //     companyCode: "DJZKJYXGS20211228201444",
          //   },
          //   company: "1475802394828308482", //公司
          //   companyName1: "电解智科技有限公司",
          //   orderRel: "",
          //   urgentTime1: "",
          // };
          const purInfo = res.data[0].purName.split('-')

          this.topInfo = {
            buyerOrg: res.data[0].purOrgId,
            buyerGroupCode: res.data[0].purOrgCode,
            //供应商，公司，采购组 ,业务类型
            employeeId: this.topInfo.employeeId,
            // buyerUser: this.topInfo.buyerUser,
            buyerUser: purInfo[2] ? purInfo[2] : purInfo[0],
            buyerUserName: purInfo[3] ? purInfo[3] : purInfo[1],
            buyerUserId: res.data[0].purId,
            purtenant: res.data[0].supplierCode,
            buyerGroup: res.data[0].purGroupCode,
            // buyerOrgId: res.data[0].purGroupId || "1435121956868947969",
            // buyerOrgName: res.data[0].purGroupName || "采购二组",
            businessTypeName: res.data[0].businessTypeName,
            businessTypeCode: res.data[0].businessTypeCode,
            businessId: res.data[0].businessTypeId,
            currencyId: res.data[0].currencyId,
            currencyName: res.data[0].currencyName,
            currencyName1: res.data[0].currencyName,
            // buyerOrg: '',
            taxTotal: '',
            freeTotal: '',
            requiredDeliveryDate: '',
            orderCode: '',
            companyInfo: {
              companyId: res.data[0].purCompanyId,
              companyName: res.data[0].purCompanyName,
              companyCode: res.data[0].purCompanyCode
            },
            company: res.data[0].purCompanyId,
            companyName1: `${res.data[0].purCompanyCode}-${res.data[0].purCompanyName}`,
            orderRel: '',
            urgentTime1: ''
          }
          this.setSupplierAndBusinessInfo(
            res.data[0].supplierId,
            res.data[0].supplierCode,
            res.data[0].businessTypeCode,
            res.data[0].businessTypeId,
            ''
          )
          this.$store.commit('updateCompanyId', res.data[0].purCompanyId)
          this.$store.commit('updateCompanyCode', res.data[0].purCompanyCode)
        }
      })
      await this.getPebusinessConfig()
      let ids = []
      dataSource.map((item) =>
        ids.push({
          lineNo: item.purRequirementLineNo,
          requestCode: item.purRequestCode
        })
      )
      this.getRequestList(ids, dataSource)
    },
    getRequestList(ids, dataSource) {
      console.log('getRequestList', dataSource)
      let params1 = {
        paramList: ids
      }
      this.$API.purchaseOrder.requestItemBatchQuery(params1).then((res) => {
        if (!res.data.length) return
        res.data.forEach((item, i) => {
          item.sourceItemId = dataSource[i].sourceItemId
          dataSource.some((item1) => {
            if (
              item1.purRequirementLineNo == item.itemNo &&
              item1.purRequestCode == item.requestCode
            ) {
              //从合同新增过来 保留申请明细 requiredId
              item.headerId = item1.contractId
              item.requiredId = item.id
              item.contractItemId = item1.contractItemId
              item.id = item1.contractItemId
              item.itemNo1 = item.itemNo
              item.itemNo = 0
              item.remainingQuantity = item1.purRequirementRemainNum || 0
              item.uniqueKey = this.addId++
              item.contractRel = null
              item.taxCode = null
              item.freePrice = null //未税单价
              item.freeTotal = null //未税总价
              item.taxPrice = null //含税单价
              item.taxTotal = null //含税总价 采购申请数据未返回默认0
              item.priceUnit = null
              item.taxid = null //税率
              if (item.budgetUnitPrice || item.budgetUnitPrice === 0) {
                item.freePrice = item.budgetUnitPrice //未税单价
              }
              if (item.budgetTotalPrice || item.budgetTotalPrice === 0) {
                item.freeTotal = item.budgetTotalPrice //未税总价
              }
              if (item.taxedUnitPrice || item.taxedUnitPrice === 0) {
                item.taxPrice = item.taxedUnitPrice //含税单价
              }
              if (item.taxedTotalPrice || item.taxedTotalPrice === 0) {
                item.taxTotal = item.taxedTotalPrice //含税总价 采购申请数据未返回默认0
              }
            }
          })
          item.remainingQuantity = dataSource[i].purRequirementRemainNum
          item.contractItemId = dataSource[i].contractItemId
          item.id = dataSource[i].contractItemId
          item.lineNo = dataSource[i].lineNo
        })
        this.getPriceList(res.data)
      })
    },
    getPriceList(dataSource) {
      console.log('getPriceList', dataSource)
      //查询价格记录 bizCode 在申请新增或者合同新增 或者申请编辑 或者合同编辑进入的时候,如果没有物料信息 才会传
      let params = []
      dataSource.forEach((item) => {
        item._flag = true
        let bizCode = item.requestCode + '_' + item.itemNo1
        let obj = {
          bizCode: '',
          itemCode: item.itemCode || '',
          siteCode: item.siteCode,
          supplierCode: this.topInfo.purtenant,
          skuCode: item.skuCode || '',
          quantity: item.remainingQuantity,
          uniqueKey: item.uniqueKey,
          sourceItemId: item.sourceItemId
        }
        if (bizCode) {
          if (!obj.itemCode) {
            obj.bizCode = bizCode
          }
        }
        if (!obj.siteCode) {
          item._flag = false
          console.log('工厂code没有阿')
          return
        }
        if (!obj.supplierCode) {
          item._flag = false
          console.log('供应商code没有阿')
          return
        }
        if (item._flag) {
          params.push(obj)
        }
      })
      let params1 = {
        orderItemList: params
      }
      this.$API.purchaseOrder.queryAndCompute(params1).then((res) => {
        let orderItemList = res.data?.orderItemList
        dataSource.forEach((item1) => {
          orderItemList.some((item2) => {
            if (item1.uniqueKey == item2.uniqueKey) {
              if (item2.computeStatus == 1 && item2.matchPriceRecordCount >= 1) {
                item1.contractRel = item2.priceRecordCode //价格协议编号1
                item1.taxCode = item2.taxCode //税率code 1
                item1.taxid = bigDecimal.multiply(item2.taxRate, 100) //税率 1
                item1.taxPrice = item2.taxPrice //含税单价 1
                item1.priceUnit = item2.priceUnit //价格单位 1
                item1.freePrice = item2.freePrice //未税单价 1
                item1.taxTotal = bigDecimal.round(item2.taxTotal, 2) //含税总价 1
                item1.freeTotal = bigDecimal.round(item2.freeTotal, 2) //未税总价 1
                return
              }
            }
          })
        })
        this.getItemByRequestInfo(dataSource)
      })
    },
    // 根据合同编码查询合同明细取含税、未税金额
    getItemByRequestInfo(dataSource) {
      console.log('getItemByRequestInfo', dataSource)
      this.$API.contract
        .getItemByRequestInfoApi({
          contractId: this.contractId,
          contractItemQueryItemParamList: dataSource.map((v) => {
            return {
              purRequestCode: v.requestCode,
              purchaseRequestNo: v.itemNo1,
              contractItemLineNo: v.lineNo
            }
          })
        })
        .then((res) => {
          if (res.code === 200) {
            res.data.forEach((v) => {
              dataSource.forEach((item) => {
                if (
                  item.requestCode === v.purRequestCode &&
                  item.itemNo1 === v.purchaseRequestNo &&
                  item.lineNo === v.lineNo
                ) {
                  item.taxPrice = v.taxedUnitPrice
                  item.taxTotal = v.taxedTotalPrice
                  item.freePrice = v.untaxedUnitPrice
                  item.freeTotal = v.untaxedTotalPrice
                }
              })
            })
          }
          this.getContractDetail(dataSource)
        })
    },
    // 需求池转合同过来的单需要另取含税、未税金额
    getContractDetail(dataSource) {
      this.$API.contract
        .getContractDetailApi({
          contractCode: this.contractCode
        })
        .then((res) => {
          if (res.code === 200) {
            if (res.data.contractVo?.contractSourceTypeCode === 1) {
              this.topInfo.currencyId = res.data.contractVo.currencyId
              this.topInfo.currencyCode = res.data.contractVo.currencyCode
              this.topInfo.currencyName = res.data.contractVo.currencyName
              this.isSourceContract = true
              let projectName = res.data.contractVo.contractName
              res.data?.contractItemVoList?.forEach((v) => {
                dataSource.forEach((item) => {
                  if (item.requestCode === v.purRequestCode) {
                    item.projectName = projectName
                    item.taxid = v.taxRate
                    item.taxCode = v.taxCode
                    item.taxPrice = v.taxedUnitPrice
                    item.taxTotal = v.taxedTotalPrice
                    item.freePrice = v.untaxedUnitPrice
                    item.freeTotal = v.untaxedTotalPrice
                  }
                })
              })
            }
          }
          this.entryOrderDetailList = dataSource
        })
    },
    async getFileNodeByDocId() {
      //编辑进入获取左边节点
      let params = {
        docId: this.entryId,
        docType: 'po'
      }
      await this.$API.purchaseOrder.getFileNodeByDocId(params).then((res) => {
        this.moduleFileList = res.data
      })
    },
    async businessTypeNameChange() {
      //业务类型改变的时候
      this.componentConfig = [] //清空tabs 数据
      this.fields = [] //清空订单明细表头
      this.moduleFileList = [] //清空附件节点
      this.entryOrderDetailList = [] //订单明细行数据
      this.entryFileList = [] //上传的附件信息
      this.entryCosts = [] //整单分摊
      this.costSwitch = '1' //是否启用整单分摊
      this.acceptanceUse = '0' //是否启用验收计划
      this.entryAcceptancePlanList = [] //带入的验收计划列表数据
      await this.getPebusinessConfig()
      if (
        (this.entryType === '2' && this.entryDraft === '1') ||
        (this.entryType === '1' && this.entrySource === '2')
      ) {
        this.entryOrderDetailList = cloneDeep(this.draftOrderDetailList)
      }
    },
    async getPebusinessConfig() {
      //获取模块配置新增
      let params = {
        businessTypeCode: this.topInfo.businessTypeCode,
        docType: 'po'
      }
      await this.$API.purchaseOrder.getPebusinessConfig(params).then((res) => {
        this.componentConfig = []
        if (res && res.data && res.data.modules) {
          res.data.modules.forEach((item) => {
            if (!(this.entryType === '1' && item.moduleName === this.$t('操作日志'))) {
              this.componentConfig.push({
                title: item.moduleName,
                moduleType: item.moduleType,
                moduleKey: item.moduleKey,
                moduleId: item.moduleId
              })
            }
            if (item.moduleType === 6) {
              if (this.entrySource == '1' || this.entrySource == '4' || this.entrySource == '2') {
                this.fields = []
                let isFC = false
                if (
                  this.topInfo.businessTypeCode === 'BTTCL001' ||
                  this.topInfo.businessTypeCode === 'BTTCL002' ||
                  this.topInfo.businessTypeCode === 'BTTCL003'
                ) {
                  isFC = true
                }
                /// 合同转订单表头不取接口数据，需前端写死
                item.fields.map((i) => {
                  if (this.entryType == '1' && this.entrySource == '4' && isFC) {
                    if (i.fieldCode === 'requiredDeliveryDate')
                      this.fields.push(
                        {
                          fieldCode: 'cycleType',
                          fieldName: this.$t('周期类型'),
                          required: 1
                        },
                        {
                          fieldCode: 'deliveryDateCycle',
                          fieldName: this.$t('交货周期'),
                          required: 1
                        },
                        {
                          fieldCode: 'consignee',
                          fieldName: this.$t('收货人'),
                          required: 1
                        },
                        {
                          fieldCode: 'contact',
                          fieldName: this.$t('联系方式'),
                          required: 1
                        },
                        {
                          fieldCode: 'receiveAddress',
                          fieldName: this.$t('收货地址'),
                          required: 1
                        },
                        i
                      )
                  } else {
                    this.fields.push(i)
                  }
                })
              }
              if (this.entrySource == '0') {
                //从缓存获取数据
                this.fields = item.fields
                let entryDataSource = cloneDeep(this.entryInfo.dataSource)
                let fileList = []
                entryDataSource.forEach((item) => {
                  if (item.file && item.file.length) {
                    item.file.forEach((item1) => {
                      fileList.push({
                        ...item1,
                        itemNo: item.itemNo || 0,
                        lineNo: item.itemNo || 0,
                        orderDetailId: item.id || 0,
                        orderId: this.entryType === '1' ? 0 : this.entryId,
                        parentId: 0,
                        remark: '',
                        remoteFileId: item1.id,
                        sysFileId: item1.id,
                        type: 0,
                        remoteUrl: item1.url,
                        url: item1.url,
                        itemCode: item.itemCode, //物料编码
                        itemName: item.itemName, //物料名称
                        skuCode: item.skuCode, //sku编码
                        skuName: item.skuName || '', //sku名称
                        specification: item.spec, //规格型号
                        createUserName: item1.createUserName || '', //创建人
                        createTime: item1.createTime || '' //创建时间
                      })
                    })
                    item.file = null
                  }
                })
                this.entryFileList = fileList
                this.entryOrderDetailList = entryDataSource
                res.data.moduleFileList.some((item1) => {
                  if (item1.nodeName === this.$t('申请明细附件')) {
                    item1.nodeCode = 'pr_item_file'
                  }
                })
              }
              this.moduleKey = item.moduleKey
            }
            if (item.moduleType === 15) {
              this.acceptanceUse = '1' //是否启用验收计划
            }
          })
          this.moduleFileList = res.data.moduleFileList
        } else {
          this.$toast({
            content: this.$t('该业务类型暂无配置'),
            type: 'warning'
          })
        }
        if (this.componentConfig.length) {
          this.currentInfo = this.componentConfig[0]
        } else {
          this.currentInfo = {}
        }
      })
    },
    async getTenantPurOrder() {
      //获取顶部详情
      await this.$API.purchaseOrder.getTenantPurOrder(this.entryId).then((res) => {
        this.topInfo = {
          ...res.data,
          purtenant: res.data.supplierCode,
          payment: res.data.paymentCode,
          buyerUser: res.data.buyerUserCode,
          buyerUserName: res.data.buyerUserName,
          buyerUserId: res.data.buyerUserId,
          companyName1: `${res.data.companyCode}-${res.data.companyName}`,
          buyerOrgName1: res.data.buyerGroupName,
          buyerOrgName2: `${res.data.buyerGroupCode}-${res.data.buyerGroupName}`,
          buyerGroupName1: `${res.data.buyerOrgCode}-${res.data.buyerOrgName}`,
          supplierName1: `${res.data.supplierCode}-${res.data.supplierName}`,
          currencyName1: `${res.data.currencyCode}-${res.data.currencyName}`,
          settlement: res.data.settlementId,
          currency: res.data.currencyCode,
          requiredDeliveryDate: new Date(Number(res.data.requiredDeliveryDate)),
          acceptanceUse: res.data.acceptanceUse.toString(),
          companyInfo: {
            companyId: res.data.companyId,
            companyName: res.data.companyName,
            companyCode: res.data.companyCode
          },
          company: res.data.companyId,
          buyerOrg: res.data.buyerGroupId,
          buyerGroup: res.data.buyerOrgCode,
          businessId: res.data.businessTypeId,
          urgentTime1: ''
        }
        this.setSupplierAndBusinessInfo(
          res.data.supplierId,
          res.data.supplierCode,
          res.data.businessTypeCode,
          res.data.businessTypeId,
          res.data.orderCode
        )
        this.$store.commit('updateCompanyId', res.data.companyId)
        this.$store.commit('updateCompanyCode', res.data.companyCode)
        this.acceptanceUse = res.data.acceptanceUse.toString()
        this.costSwitch = res.data.allocationMode.toString()
      })
    },
    setSupplierAndBusinessInfo(
      supplierId,
      supplierCode,
      businessTypeCode,
      businessTypeId,
      orderCode
    ) {
      sessionStorage.setItem('supplierId', supplierId)
      sessionStorage.setItem('supplierCode', supplierCode)
      sessionStorage.setItem(
        'businessInfo',
        JSON.stringify({
          businessTypeCode: businessTypeCode,
          businessId: businessTypeId,
          orderCode: orderCode
        })
      )
    },
    async getPurOrderModuleData() {
      // 获取模块详情
      let params = {
        businessType: this.topInfo.businessTypeId,
        orderId: this.entryId,
        orderType: this.topInfo.type,
        purOrderCode: '',
        requestParams: {
          defaultRules: [],
          page: {
            current: 1,
            size: 1000000000
          }
        }
      }
      if (this.entryType === '3') {
        params.requestParams.page.size = 50
      } else {
        params.requestParams.page.size = 1000000000
      }
      await this.getPurOrderModuleData1(params) //查询订单明细
      if (this.costSwitch === '0') {
        //整单分摊
        await this.getPurOrderModuleData3(params)
      }
      if (this.acceptanceUse === '1') {
        //验收计划
        await this.getPurOrderModuleData4(params)
      }
      await this.getPurOrderModuleData5(params) //合作方
    },
    async getPurOrderModuleData5(params1) {
      //合作方
      let params = {
        ...params1,
        moduleId: this.componentConfig.find((item) => {
          return item.moduleType === 17
        })?.moduleId,
        moduleKey: this.componentConfig.find((item) => {
          return item.moduleType === 17
        })?.moduleKey
      }
      await this.$API.purchaseOrder.getPurOrderModuleData(params).then((res) => {
        this.entryPartners = res.data.partners || []
      })
    },
    async getPurOrderModuleData4(params1) {
      //验收计划的
      let params = {
        ...params1,
        moduleId: this.componentConfig.find((item) => {
          return item.moduleType === 15
        })?.moduleId,
        moduleKey: this.componentConfig.find((item) => {
          return item.moduleType === 15
        })?.moduleKey
      }
      await this.$API.purchaseOrder.getPurOrderModuleData(params).then((res) => {
        this.entryAcceptancePlanList = res.data.acceptances || []
      })
    },
    updateGetModuleData1(currentPage, pageSize) {
      console.log(currentPage, pageSize, '我是传的页数')
      let params = {
        businessType: this.topInfo.businessTypeId,
        orderId: this.entryId,
        orderType: this.topInfo.type,
        purOrderCode: '',
        requestParams: {
          defaultRules: [],
          page: {
            current: currentPage,
            size: pageSize
          }
        }
      }
      this.getPurOrderModuleData1(params)
    },
    async getPurOrderModuleData1(params1) {
      //订单明细的
      let params = {
        ...params1,
        moduleId: this.componentConfig.find((item) => {
          return item.moduleType === 6
        })?.moduleId,
        moduleKey: this.componentConfig.find((item) => {
          return item.moduleType === 6
        })?.moduleKey
      }
      await this.$API.purchaseOrder.getPurOrderModuleData(params).then((res) => {
        this.entryOrderDetailList = res.data.purOrderDetailResponse.records
        if (this.entryType === '3') {
          this.totalRecordsCount = res.data.purOrderDetailResponse.total
        }

        this.draftOrderDetailList = cloneDeep(res.data.purOrderDetailResponse.records)
      })
    },
    async getPurOrderModuleData2() {
      //附件带入的信息
      let params = {
        docId: this.entryId,
        parentId: 0,
        docType: 'po'
        // so
      }
      await this.$API.purchaseOrder.queryFileByDocId(params).then((res) => {
        let entryFileList = []
        res.data = res.data || []
        res.data.forEach((item) => {
          if (item.nodeType === 1) {
            item.id = item.sysFileId
            item.remoteUrl = item.url
            entryFileList.push(item)
          }
        })
        this.entryFileList = entryFileList
      })
    },
    async getPurOrderModuleData3(params1) {
      //整单分摊的
      let params = {
        ...params1,
        moduleId: this.componentConfig.find((item) => {
          return item.title === this.$t('整单分摊')
        })?.moduleId,
        moduleKey: this.componentConfig.find((item) => {
          return item.title === this.$t('整单分摊')
        })?.moduleKey
      }
      await this.$API.purchaseOrder.getPurOrderModuleData(params).then((res) => {
        let entryCosts = res.data.orderCost?.records || []
        entryCosts.forEach((item, index1) => {
          item.index = index1 + 1
        })
        this.entryCosts = entryCosts
      })
    },
    async getPurOrderModuleConfig() {
      //获取模块配置编辑
      let params = {
        docId: this.entryId
      }
      await this.$API.purchaseOrder.getPurOrderModuleConfig(params).then((res) => {
        res.data.moduleItems.forEach((item) => {
          this.componentConfig.push({
            title: item.moduleName,
            moduleType: item.moduleType,
            moduleKey: item.moduleKey,
            moduleId: item.moduleId
          })
          if (item.moduleType === 6) {
            this.fields = item.fieldDefines
            this.moduleKey = item.moduleKey
          }
        })
        this.moduleFileList = res.data.moduleFileList
        this.currentInfo = this.componentConfig[0]
      })
    },
    judgeCostPercentage() {
      //启用了整单分摊 ,校验比例和为100才可以提交
      console.log(this.costsList)
      let allPercentage = '0'
      this.costsList.forEach((item) => {
        allPercentage = bigDecimal.add(item.percentage, allPercentage)
      })
      if (bigDecimal.compareTo(allPercentage, 100) !== 0) {
        this.$toast({
          content: this.$t('启用整单分摊,比例和需要达到100%'),
          type: 'error'
        })
        return false
      } else {
        return true
      }
    },
    updateTopInfoUrgentime1(type) {
      this.$refs.topInfo.updateTopInfoUrgentime1(type)
    },
    updateTopInfoUrgentime(type) {
      console.log(type)
      this.$refs.topInfo.updateTopInfoUrgentime(type)
    },
    expeditedAllDetail() {
      //加急所有明细行
      if (this.$refs.dataGrid.isEdit === '1') {
        this.$refs.dataGrid.endEdit()
        this.$refs.dataGrid.expeditedAllDetail()
      } else {
        this.$refs.dataGrid.expeditedAllDetail()
      }
    },
    cancleExpeditedAllDetail() {
      //取消加急所有明细行
      if (this.$refs.dataGrid.isEdit === '1') {
        this.$refs.dataGrid.endEdit()
        this.$refs.dataGrid.cancleExpeditedAllDetail()
      } else {
        this.$refs.dataGrid.cancleExpeditedAllDetail()
      }
    },
    async save(type) {
      //  需要多一个判断 订单明细中是否有含税单价和未税单价
      if (this.$refs.dataGrid.isEdit === '1') {
        //判断是否在编辑 ,如果编辑等100毫秒等订单明细组件传出数据
        this.$refs.dataGrid.endEdit()
        await setTimeout(() => {}, 600)
      }
      if (this.$refs.acceptancePlan.isEdit === '1') {
        //判断是否在编辑 ,如果编辑等100毫秒等订单明细组件传出数据
        this.$refs.acceptancePlan.endEdit()
        await setTimeout(() => {}, 600)
      }
      if (this.$refs.partners.isEdit === '1') {
        //判断是否在编辑 ,如果编辑等100毫秒等订单明细组件传出数据
        this.$refs.partners.endEdit()
        await setTimeout(() => {}, 600)
      }
      // if (this.acceptanceUse === "1" && !this.validateAcceptanceType) {
      //   this.$toast({
      //     content: "请检查选择验收类型",
      //     type: "error",
      //   });
      //   return;
      // }
      // if (this.acceptanceUse === "1" && !this.validatePercentage) {
      //   this.$toast({
      //     content: "请检查输入有效的验收计划比例",
      //     type: "error",
      //   });
      //   return;
      // }
      // if (this.acceptanceUse === "1" && !this.allIsPercentage) {
      //   //启用验收计划并且比例和不是100%
      //   this.$toast({
      //     content: "请检查所有订单明细关联的验收计划比例和需要为100%",
      //     type: "error",
      //   });
      //   return;
      // }
      // if (this.costSwitch === "0") {
      //   if (!this.judgeCostPercentage()) return;
      // }
      let params = this.getParams(this.$refs.topInfo.getParams(), 1)
      let orderDetailHasUrgentTime = false
      let nowUrgentTime = ''
      params.orderDetails.forEach((item) => {
        if (item.urgentTime) {
          orderDetailHasUrgentTime = true
          nowUrgentTime = item.urgentTime
        }
      })
      if (orderDetailHasUrgentTime) {
        if (!params.order.urgentTime || !params.order.urgentStatus) {
          params.order.urgentTime = new Date(nowUrgentTime).getTime()
          params.order.urgentStatus = 1
        }
      }
      if (!this.$refs.dataGrid.validateTable1()) return
      // let flag = await this.$refs.dataGrid.validateJudgeList();
      // if (!flag) return;
      this.$store.commit('startLoading')
      this.$API.purchaseOrder.purOrderDraft(params).then(() => {
        this.$store.commit('endLoading')
        if (type === 'publish') {
          this.$toast({ content: this.$t('发布成功'), type: 'success' })
        } else {
          this.$toast({ content: this.$t('保存草稿操作成功'), type: 'success' })
        }
        this.$router.push({
          name: 'purchase-coordination'
        })
      })
    },
    async publish() {
      await this.submit('publish')
    },
    async submit(type = 'submit') {
      if (this.$refs.dataGrid.isEdit === '1') {
        //判断是否在编辑 ,如果编辑等100毫秒等订单明细组件传出数据
        this.$refs.dataGrid.endEdit()
        await setTimeout(() => {}, 600)
      }
      if (this.$refs.acceptancePlan.isEdit === '1') {
        //判断是否在编辑 ,如果编辑等100毫秒等订单明细组件传出数据
        this.$refs.acceptancePlan.endEdit()
        await setTimeout(() => {}, 600)
      }
      if (this.$refs.partners.isEdit === '1') {
        //判断是否在编辑 ,如果编辑等100毫秒等订单明细组件传出数据
        this.$refs.partners.endEdit()
        await setTimeout(() => {}, 600)
      }
      let params = this.getParams(this.$refs.topInfo.getParams(), 2)
      if (!params.orderDetails.length) {
        this.$toast({
          content: this.$t('提交订单至少添加一条订单明细'),
          type: 'error'
        })
        return
      }
      if (!this.$refs.dataGrid.validateTable()) return
      if (
        this.topInfo.businessTypeCode === 'BTTCL001' ||
        this.topInfo.businessTypeCode === 'BTTCL002' ||
        this.topInfo.businessTypeCode === 'BTTCL003'
      ) {
        for (let i = 0; i < params.orderDetails.length; i++) {
          let details = params.orderDetails[i]
          if (!details?.purOrderDetailRequire?.cycleType) {
            this.$toast({ content: this.$t('订单明细周期类型不可为空'), type: 'error' })
            return
          }
          if (!details?.purOrderDetailRequire?.deliveryDateCycle) {
            this.$toast({ content: this.$t('订单明细交货周期不可为空'), type: 'error' })
            return
          }
        }
      }
      let flag = await this.$refs.dataGrid.validateJudgeList()
      if (!flag) return
      console.log(params.orderDetails, '我是要提交的明细行信息')
      let orderDetailHasUrgentTime = false
      let nowUrgentTime = ''
      params.orderDetails.forEach((item) => {
        if (item.urgentTime) {
          orderDetailHasUrgentTime = true
          nowUrgentTime = item.urgentTime
        }
        if (!item.freePrice) {
          item.freePrice = 0
        }
        if (!item.freeTotal) {
          item.freeTotal = 0
        }
        if (!item.taxPrice) {
          item.taxPrice = 0
        }
        if (!item.taxTotal) {
          item.taxTotal = 0
        }
        if (!item.unPrice) {
          item.unPrice = 0
        }
        if (!item.unTotal) {
          item.unTotal = 0
        }
        if (!item.approvedTotalPrice) {
          item.approvedTotalPrice = 0
        }
        if (!item.budgetTotalPrice) {
          item.budgetTotalPrice = 0
        }
        if (!item.budgetUnitPrice) {
          item.budgetUnitPrice = 0
        }
        if (!item.subjectTotal) {
          item.subjectTotal = 0
        }
        if (!item.taxedTotalPrice) {
          item.taxedTotalPrice = 0
        }
        if (!item.taxedUnitPrice) {
          item.taxedUnitPrice = 0
        }
      })
      if (orderDetailHasUrgentTime) {
        if (!params.order.urgentTime || !params.order.urgentStatus) {
          params.order.urgentTime = new Date(nowUrgentTime).getTime()
          params.order.urgentStatus = 1
        }
      }
      if (this.acceptanceUse === '1' && !this.validateAcceptanceType) {
        this.$toast({
          content: this.$t('请检查选择验收类型'),
          type: 'error'
        })
        return
      }
      if (this.acceptanceUse === '1' && !this.validateAcceptor) {
        this.$toast({
          content: this.$t('请检查选择验收人'),
          type: 'error'
        })
        return
      }
      if (this.acceptanceUse === '1' && !this.validateacceptanceQuantity) {
        this.$toast({
          content: this.$t('请检查验收数量需要大于0'),
          type: 'error'
        })
        return
      }
      if (this.acceptanceUse === '1' && !this.validatefreeTotal) {
        this.$toast({
          content: this.$t('请检查验收计划未税金额需要大于0'),
          type: 'error'
        })
        return
      }
      if (this.acceptanceUse === '1' && !this.validatetaxTotal) {
        this.$toast({
          content: this.$t('请检查验收计划含税金额需要大于0'),
          type: 'error'
        })
        return
      }
      if (this.acceptanceUse === '1') {
        if (!this.judgeValidAcceptance(params)) {
          return
        }
      }
      if (this.costSwitch === '0') {
        if (!this.judgeCostPercentage()) return
      }
      if (this.partners.length && !this.allValidatePartner) {
        this.$toast({
          content: this.$t('请检查合作方数据 且不同行合作类型不能重复'),
          type: 'error'
        })
        return
      }
      console.log(params, '我是要提交的信息')

      this.$store.commit('startLoading')
      this.$API.purchaseOrder.purOrderSave(params).then(() => {
        if (type === 'publish') {
          this.save(type)
        } else {
          this.$store.commit('endLoading')
          this.$toast({ content: this.$t('提交订单操作成功'), type: 'success' })
          // this.$router.push({
          //   name: 'purchase-coordination'
          // })
          // this.$router.push(`/middlePlatform/contractBuyerList`)
          this.$router.go(-1)
        }
      })
    },
    judgeValidAcceptance(params) {
      //校验验收计划的数据
      let flag = true
      let str = ''
      let orderDetails = cloneDeep(params.orderDetails)
      orderDetails.forEach((item) => {
        let acceptanceTypeList = []
        let sameAcceptanceType = {}
        console.log(item, '我是校验的没一下')
        if (item.closeStatus != 1) {
          item.acceptances.forEach((item1) => {
            if (acceptanceTypeList.indexOf(item1.acceptanceType) === -1) {
              acceptanceTypeList.push(item1.acceptanceType)
            }
          })
          acceptanceTypeList.forEach((item2) => {
            sameAcceptanceType[item2] = []
            item.acceptances.some((item3) => {
              if (item2 === item3.acceptanceType) {
                sameAcceptanceType[item2].push(item3)
                return
              }
            })
          })
          Object.keys(sameAcceptanceType).forEach((itme4) => {
            let quantity = sameAcceptanceType[itme4][0].quantity
            let acceptanceQuantity = ''
            sameAcceptanceType[itme4].forEach((itme5) => {
              acceptanceQuantity = bigDecimal.add(acceptanceQuantity, itme5.acceptanceQuantity)
            })
            if (bigDecimal.compareTo(quantity, acceptanceQuantity) !== 0) {
              flag = false
              str = this.$t('相同验收类型的验收数量需要等于订单明细的数量')
            }
          })
        }
      })
      orderDetails.forEach((item) => {
        if (item.acceptances.length) {
          let freeTotal1 = item.acceptances[0].freeTotal1
          let allFreeTotal = ''
          let allTaxTotal = ''
          let taxTotal1 = item.acceptances[0].taxTotal1
          if (item.closeStatus != 1) {
            item.acceptances.forEach((item1) => {
              allFreeTotal = bigDecimal.add(item1.freeTotal, allFreeTotal)
              allTaxTotal = bigDecimal.add(item1.taxTotal, allTaxTotal)
            })
            if (bigDecimal.compareTo(freeTotal1, allFreeTotal) !== 0) {
              flag = false
              // str = str || `订单明细关联的验收计划未税金额需要等于订单明细未税总价`
              str =
                str ||
                `订单明细关联的验收计划未税金额与订单明细未税总价相差${bigDecimal.subtract(
                  freeTotal1,
                  allFreeTotal
                )}`
            }
            if (bigDecimal.compareTo(taxTotal1, allTaxTotal) !== 0) {
              flag = false
              // str = str || '订单明细关联的验收计划含税金额需要等于订单明细含税总价'
              str =
                str ||
                `订单明细关联的验收计划含税金额与订单明细含税总价相差${bigDecimal.subtract(
                  taxTotal1,
                  allTaxTotal
                )}`
            }
          }
        }
      })
      if (!flag) {
        this.$toast({
          content: this.$t(str),
          type: 'error'
        })
      }
      return flag
    },
    updateEntryFileList(fileList) {
      //订单明细附件有修改之后同步给相关附件
      this.newFileList = fileList
    },
    updateOrderInfo(orderTaxTotal, orderFreeTotal, orderRequiredDate) {
      //更新要求交期 未税总金额 含税总金额
      this.topInfo.taxTotal = orderTaxTotal
      this.topInfo.freeTotal = orderFreeTotal
      if (orderRequiredDate) {
        this.topInfo.requiredDeliveryDate = new Date(Number(orderRequiredDate))
      } else {
        this.topInfo.requiredDeliveryDate = ''
      }
    },
    updateGrid(e, maxItemNo) {
      this.gridList = e
      this.maxItemNo = maxItemNo
    },
    updateCosts(e, openSwitch) {
      this.costSwitch = openSwitch ? '0' : '1'
      if (this.costSwitch === '0') {
        this.costsList = e
      } else {
        this.costsList = []
      }
    },
    updateFile(e) {
      this.fileList = e
    },
    handleSelectTab(index, item) {
      this.currentInfo = item
      if (this.currentInfo.title !== this.$t('订单明细')) {
        if (this.$refs.dataGrid.isEdit === '1') {
          //判断是否在编辑 ,如果编辑等100毫秒等订单明细组件传出数据
          this.$refs.dataGrid.endEdit()
        }
      }
      if (this.currentInfo.title !== this.$t('验收计划')) {
        if (this.$refs.acceptancePlan.isEdit === '1') {
          //判断是否在编辑 ,如果编辑等100毫秒等订单明细组件传出数据
          this.$refs.acceptancePlan.endEdit()
        }
      }
      if (this.currentInfo.title !== this.$t('合作方')) {
        if (this.$refs.partners.isEdit === '1') {
          //判断是否在编辑 ,如果编辑等100毫秒等订单明细组件传出数据
          this.$refs.partners.endEdit()
        }
      }
    },
    getParams(order, type) {
      let params = {
        moduleKey: this.moduleKey,
        order: {
          ...order,
          status:
            (type == 2 && this.entryType === '1') ||
            (this.entryType === '2' && this.entryDraft !== '1')
              ? 1
              : 0, //新增提交或者编辑提交
          originSource: this.entrySource
        },
        maxItemNo: this.maxItemNo,
        orderCosts: this.costsList,
        orderDetails: this.gridList,
        orderFiles: this.fileList,
        orderId: 0,
        source: this.entrySource,
        originSource: this.entrySource,
        partners: this.partners
      }
      //编辑的时候
      if (type === 2 && this.entryType === '2') {
        params.orderId = this.entryId
      }
      //编辑草稿的时候
      if (
        type === 1 &&
        this.entryType === '2' &&
        this.entrySource === '1' &&
        this.entryDraft === '1'
      ) {
        params.orderId = this.entryId
      }
      return params
    }
  },
  beforeDestroy() {
    if (sessionStorage.getItem('purchaseRequestSession')) {
      sessionStorage.removeItem('purchaseRequestSession')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .top-info {
  margin-top: 20px;
}
.bottom-tables {
  /deep/ .mt-tabs {
    flex-shrink: 0;
    .mt-tabs-container {
      width: 100%;
    }
  }
}
</style>
