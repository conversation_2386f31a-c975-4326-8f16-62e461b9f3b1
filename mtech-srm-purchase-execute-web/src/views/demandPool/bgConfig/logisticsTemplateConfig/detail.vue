<template>
  <div class="full-height pt20">
    <div class="rule-box">
      <div class="main-info">
        <div class="code">
          <span class="label">{{ $t('模板名称：') }} </span>
          <span class="name">{{ templateRow.templateCode }} - {{ templateRow.templateName }}</span>
        </div>
      </div>
      <div class="returns" @click="handleBack">{{ $t('返回') }}</div>
    </div>

    <mt-template-page
      ref="templateDetailRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @dataBound="handleDataBound"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleChangeValue="handleChangeValue"
    ></mt-template-page>
    <add-detail ref="addDetail" @confirmSuccess="confirmSuccess"></add-detail>
  </div>
</template>

<script>
import addDetail from './component/addDetail.vue'
import { columnData } from './config/detail'
export default {
  components: {
    addDetail
  },
  data() {
    return {
      templateRow: {
        templateCode: '',
        templateName: ''
      },
      cacheData: [],
      selectedList: [],
      cacheSelectedData: [],
      pageConfig: null
    }
  },
  computed: {
    templateId() {
      return this.$route.query.tid
    }
  },

  async created() {
    const _templateRow = JSON.parse(localStorage.getItem('templateRow'))
    this.templateRow = { ..._templateRow }
    await this.getSelectedData()
  },
  methods: {
    initPageCofig() {
      return [
        {
          useToolTemplate: false,
          useBaseConfig: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              this.$route.query?.type === 'view'
                ? []
                : [{ id: 'Save', icon: '', title: this.$t('保存') }],
              []
            ]
          },
          grid: {
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            columnData: columnData(this),
            dataSource: [],
            pageSettings: {
              currentPage: 1,
              pageSize: 1000,
              pageSizes: [1000],
              totalRecordsCount: 0
            },
            asyncConfig: {
              url: '/contract/tenant/request/config/field/list',
              serializeList: (list) => {
                list.forEach((item) => {
                  const _find = this.selectedList.find((_item) => _item.fieldId === item.id)
                  if (_find) {
                    item.checked = true
                    item.seqNo = _find.seqNo
                    item.required = !!_find.required
                  } else {
                    item.seqNo = ''
                  }
                })
                return list
              },
              afterAsyncData: (res) => {
                this.cacheData = res.data?.records
              }
            }
          }
        }
      ]
    },
    handleClickCellTool(e) {
      let _row = e.data
      if (e.tool.id == 'AddStepList') {
        this.handleAddStepList(_row)
      }
    },
    handleAddStepList(row) {
      let _find = this.cacheData.find((item) => item.id === row.id)
      if (_find.checked) {
        let _selectedData = this.cacheSelectedData.find((item) => item.fieldId === row.id)
        _find.stepList = _selectedData?.stepList || []
        this.$refs.addDetail.initSidebar(_find, this.cacheData, this.templateId)
      } else {
        this.$toast({ content: this.$t('请先勾选数据'), type: 'warning' })
      }
    },
    async getSelectedData() {
      await this.$API.bgConfig
        .getLogisticsTempalteList({ templateId: this.templateId })
        .then((res) => {
          if (res.code === 200) {
            this.selectedList = [...res.data]
            this.cacheSelectedData = [...res.data]
            this.pageConfig = this.initPageCofig()
          }
        })
        .catch(() => {
          this.$hloading()
        })
    },
    handleDataBound() {},
    handleClickToolBar() {
      this.handleSave()
    },
    handleChangeValue(e) {
      this.cacheData.forEach((item, index) => {
        if (index === Number(e.index)) {
          item[e.key] = e.value
        }
      })
    },
    handleSave() {
      let fieldList = []
      this.cacheData.forEach((item) => {
        if (item.checked) {
          let stepList =
            this.cacheSelectedData.find((selectedItem) => selectedItem.fieldId === item.id)
              ?.stepList || null
          fieldList.push({
            fieldId: item.id,
            seqNo: item.seqNo,
            required: item.required ? 1 : 0,
            stepList
          })
        }
      })
      this.$API.bgConfig
        .setLogisticsTempalte({ templateId: this.templateId, fieldList })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
          }
        })
        .catch(() => {
          this.$hloading()
        })
    },

    handleBack() {
      this.$router.go(-1)
    },
    confirmSuccess() {
      this.getSelectedData()
      this.$refs.templateDetailRef.refreshCurrentGridData()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  display: flex;
  flex-direction: column;
  .common-template-page {
    flex: 1;
  }
}
.rule-box {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;

  .main-info {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    .code {
      flex: 1;
      margin: 0 10px;
      color: #212121;
      .label {
        font-size: 16px;
        font-weight: bold;
      }
      .name {
        font-size: 16px;
      }
    }
  }

  .returns {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
    padding: 10px;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 1cap;
  }
}
</style>
