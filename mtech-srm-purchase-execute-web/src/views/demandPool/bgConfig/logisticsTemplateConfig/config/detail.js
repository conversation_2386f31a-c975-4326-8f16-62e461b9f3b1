import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = (that) => {
  const type = that.$route.query?.type
  return [
    {
      field: 'checked',
      headerText: i18n.t('是否需要'),
      width: 150,
      template: () => {
        return {
          template: Vue.component('sourt', {
            template: `
               <mt-checkbox v-model="data.checked" @change="handleChange" :disabled="type==='view'"></mt-checkbox>
            `,
            data() {
              return { data: {}, type: type }
            },

            methods: {
              handleChange(e) {
                this.data.checked = e.checked
                this.$parent.$emit('handleChangeValue', {
                  index: this.data.index,
                  key: 'checked',
                  value: e.checked
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'seqNo',
      headerText: i18n.t('排序'),
      width: 150,
      template: () => {
        return {
          template: Vue.component('sort', {
            template: `
               <mt-input-number
                :id="data.seqNo" :min="1" :show-spin-button="false" v-model="data.seqNo" @change="handleChange" :disabled="type==='view'"/>
            `,
            data() {
              return { type: type }
            },
            methods: {
              handleChange(e) {
                this.data.seqNo = e
                this.$parent.$emit('handleChangeValue', {
                  index: this.data.index,
                  key: 'seqNo',
                  value: e
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'fieldName',
      headerText: i18n.t('字段名称'),
      width: 400,
      allowEditing: false,
      cellTools: [
        {
          id: 'AddStepList',
          icon: 'icon_solid_Createorder',
          title: i18n.t('阶梯明细'),
          visibleCondition: (data) => data.fieldCode.includes('step')
        }
      ]
    },
    {
      field: 'fieldCode',
      headerText: i18n.t('字段编码'),
      width: 400,
      allowEditing: false
    },
    {
      field: 'required',
      headerText: i18n.t('是否必填'),
      width: 150,
      template: () => {
        return {
          template: Vue.component('sourt', {
            template: `
               <mt-checkbox v-model="data.required" @change="handleChange" :disabled="type==='view'"></mt-checkbox>
            `,
            data() {
              return { data: {}, type: type }
            },

            methods: {
              handleChange(e) {
                this.data.required = e.checked
                this.$parent.$emit('handleChangeValue', {
                  index: this.data.index,
                  key: 'required',
                  value: e.checked
                })
              }
            }
          })
        }
      }
    }
  ]
}
