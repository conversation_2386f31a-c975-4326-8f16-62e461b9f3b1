<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules" style="padding-top: 20px">
      <mt-form-item prop="seqNo" :label="$t('序号')">
        <mt-input-number
          v-model="addForm.seqNo"
          :min="1"
          :show-spin-button="false"
          :show-clear-button="true"
          :placeholder="$t('请输入序号')"
        />
      </mt-form-item>
      <mt-form-item prop="fieldCode" :label="$t('字段编码')">
        <mt-input
          v-model.trim="addForm.fieldCode"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入字段编码')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="fieldName" :label="$t('字段名称')">
        <mt-input
          v-model.trim="addForm.fieldName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入字段名称')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="groupCode" :label="$t('分组编码')">
        <mt-input
          v-model.trim="addForm.groupCode"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入分组编码')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="groupName" :label="$t('分组名称')">
        <mt-input
          v-model.trim="addForm.groupName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入分组名称')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="displayType" :label="$t('展示类型')">
        <mt-select
          v-model.trim="addForm.displayType"
          :data-source="displayTypeList"
          :show-clear-button="true"
          :fields="{ text: 'label', value: 'value' }"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请选择展示类型')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="fieldDesc" :label="$t('字段描述')">
        <mt-input
          v-model.trim="addForm.fieldDesc"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入字段描述')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="dictCode" :label="$t('字典编码')">
        <mt-input
          v-model.trim="addForm.dictCode"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入字典编码')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        seqNo: '',
        fieldCode: '',
        fieldName: '',
        groupCode: '',
        groupName: '',
        displayType: '',
        fieldDesc: ''
      },
      rules: {
        seqNo: [
          {
            required: true,
            message: this.$t('请输入序号'),
            trigger: 'blur'
          }
        ],
        fieldCode: [
          {
            required: true,
            message: this.$t('请输入字段编码'),
            trigger: 'blur'
          }
        ],
        fieldName: [
          {
            required: true,
            message: this.$t('请输入字段名称'),
            trigger: 'blur'
          }
        ],
        displayType: [
          {
            required: true,
            message: this.$t('请选择展示类型'),
            trigger: 'blur'
          }
        ]
      },
      displayTypeList: [
        {
          label: this.$t('正常'),
          value: 1
        },
        {
          label: this.$t('向下展示'),
          value: 2
        },
        {
          label: this.$t('平铺展示'),
          value: 3
        }
      ]
    }
  },

  mounted() {
    this.cacheAddForm = {
      ...this.addForm
    }
  },

  methods: {
    dialogInit(args) {
      const { dialogType, rowData } = args
      if (dialogType == 'add') {
        this.addForm = {
          ...this.cacheAddForm
        }
        this.dialogTitle = this.$t('新增')
      } else {
        this.dialogTitle = this.$t('编辑')
        const {
          id,
          seqNo,
          fieldCode,
          fieldName,
          groupCode,
          groupName,
          displayType,
          fieldDesc,
          dictCode
        } = rowData
        this.addForm = {
          ...this.addForm,
          ...{
            id,
            seqNo,
            fieldCode,
            fieldName,
            groupCode,
            groupName,
            displayType,
            fieldDesc,
            dictCode
          }
        }
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.addForm
          }
          this.$API.bgConfig.logisticsFieldUpdate(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$refs.dialog.ejsRef.hide()
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    }
  }
}
</script>
