<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="status mr20">
        {{ headerInfo && headerInfo.statusTxt }}
      </div>
      <div class="infos mr20" v-if="headerInfo && headerInfo.docNo">
        {{ (headerInfo && headerInfo.docNo) || '-' }}
      </div>
      <div class="infos mr20">
        {{ $t('创建人：') }}{{ headerInfo && headerInfo.createUserName }}
      </div>
      <div class="infos mr20" v-if="headerInfo && headerInfo.createTime">
        {{ $t('创建时间：') }}{{ headerInfo.createTime | formatTime }}
      </div>
      <div class="infos" v-if="headerInfo && headerInfo.updateTime">
        {{ $t('单据更新时间：') }}{{ headerInfo.updateTime | formatTime }}
      </div>

      <div class="middle-blank"></div>

      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" v-if="canSave" @click="handleSave()">{{
        $t('保存草稿')
      }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" v-if="canSubmit" @click="handleSubmit()">{{
        $t('提交')
      }}</mt-button>

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
          <mt-select
            ref="businessRef"
            v-model="addForm.businessTypeId"
            :data-source="businessTypeList"
            :allow-filtering="true"
            filter-type="Contains"
            disabled
            :show-clear-button="false"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择业务类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="demandType" :label="$t('需求类型')">
          <mt-select
            ref="demandTypeRef"
            v-model="addForm.demandType"
            :data-source="demandTypeList"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="false"
            :disabled="type !== 'add'"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择需求类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="logisticsMethodCode" :label="$t('物流方式')">
          <mt-select
            ref="logisticsMethodRef"
            v-model="addForm.logisticsMethodCode"
            :data-source="logisticsMethodList"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="false"
            :disabled="type !== 'add'"
            :placeholder="$t('请选择物流方式')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="templateCode" :label="$t('模板')">
          <mt-select
            ref="templateRef"
            v-model="addForm.templateCode"
            :data-source="templateCodeList"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="false"
            :disabled="type !== 'add'"
            :placeholder="$t('请选择模板')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="processDocName" :label="$t('名称')">
          <mt-input
            v-model="addForm.processDocName"
            :disabled="type !== 'add'"
            :maxlength="50"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="purchaserId" :label="$t('采购员')" class="apply-item">
          <mt-input v-if="type === 'view'" v-model="addForm.purchaserName" disabled></mt-input>
          <debounce-filter-select
            v-else
            ref="purchaserRef"
            v-model="addForm.purchaserId"
            :request="getPurchaserUser"
            :data-source="purchaserUserIdData"
            :show-clear-button="false"
            :fields="{ text: 'text', value: 'uid' }"
            @change="purchaserChange"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item prop="applyUserId" :label="$t('申请人')" class="apply-item">
          <mt-input v-if="type === 'view'" v-model="addForm.applyUserName" disabled></mt-input>
          <debounce-filter-select
            ref="userRef"
            v-else
            v-model="addForm.applyUserId"
            :request="getUser"
            :data-source="applyUserIdData"
            :show-clear-button="false"
            :fields="{ text: 'text', value: 'employeeId' }"
          ></debounce-filter-select>
        </mt-form-item>

        <mt-form-item prop="administrationCompanyId" :label="$t('行政公司')" class="apply-item">
          <mt-input
            v-if="type === 'view'"
            disabled
            v-model="addForm.administrationCompanyName"
          ></mt-input>

          <!-- 共享财务推送过来的数据，只展示 -->
          <mt-select
            ref="companyRef"
            v-else
            v-model="addForm.administrationCompanyId"
            :data-source="applyCompanyData"
            :show-clear-button="false"
            :allow-filtering="true"
            filter-type="Contains"
            :filtering="getCompany"
            :fields="{ text: 'labelShow', value: 'id' }"
            popup-width="350"
            @change="handleCompanyChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="companyId" :label="$t('业务公司')" class="apply-item">
          <mt-input v-if="type === 'view'" disabled v-model="addForm.companyName"></mt-input>
          <mt-select
            ref="companyRef1"
            v-else
            :popup-width="350"
            v-model="addForm.companyId"
            :data-source="companyOptions"
            :show-clear-button="false"
            :fields="{ text: 'labelShow', value: 'id' }"
            :allow-filtering="true"
            filter-type="Contains"
            :filtering="getCompanyOptions"
            @change="handleCompanyChange1"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="applyDepId" :label="$t('申请部门')">
          <mt-input v-if="type === 'view'" disabled v-model="addForm.applyDepName"></mt-input>
          <mt-select
            ref="depRef"
            v-else
            v-model="addForm.applyDepId"
            :data-source="applyDepartData"
            :show-clear-button="false"
            :allow-filtering="true"
            filter-type="Contains"
            :fields="{ text: 'orgName', value: 'id' }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryId" :label="$t('品类名称')" class="apply-item">
          <mt-input v-if="type === 'view'" disabled v-model="addForm.categoryName"></mt-input>
          <RemoteAutocomplete
            v-else
            ref="categoryRef"
            v-model="addForm.categoryId"
            url="/sourcing/tenant/permission/queryCategorys"
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryCode-categoryName', value: 'id' }"
            :search-fields="['categoryName', 'categoryCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="polName" :label="$t('POL')" v-if="addForm.logisticsMethodCode === '1'">
          <mt-input
            v-model="addForm.polCode"
            :disabled="type === 'view'"
            :maxlength="50"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="podName" :label="$t('POD')" v-if="addForm.logisticsMethodCode === '1'">
          <mt-input
            v-model="addForm.podCode"
            :disabled="type === 'view'"
            :maxlength="50"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="lineName"
          :label="$t('TCL LINE')"
          v-if="addForm.logisticsMethodCode === '1'"
        >
          <mt-input
            v-model="addForm.lineCode"
            :disabled="type === 'view'"
            :maxlength="50"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="mainLineName"
          :label="$t('MAIN LINE')"
          v-if="addForm.logisticsMethodCode === '1'"
        >
          <mt-input
            v-model="addForm.mainLineName"
            :disabled="type === 'view'"
            :maxlength="50"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="countryName"
          :label="$t('国家')"
          v-if="addForm.logisticsMethodCode === '1'"
        >
          <mt-input
            v-model="addForm.countryName"
            :disabled="type === 'view'"
            :maxlength="50"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="railwayMode"
          :label="$t('模式')"
          v-if="addForm.logisticsMethodCode === '3'"
        >
          <mt-input v-if="type === 'view'" disabled v-model="addForm.railwayModeName"></mt-input>
          <mt-select
            ref="railwayModeRef"
            v-else
            v-model="addForm.railwayMode"
            :data-source="railwayModeList"
            :show-clear-button="false"
            :allow-filtering="true"
            filter-type="Contains"
            :fields="{ text: 'dictName', value: 'dictCode' }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="addForm.remark"
            :multiline="true"
            :maxlength="200"
            :disabled="type === 'view'"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { timeNumberToDate } from '@/utils/utils'
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isInit: true, // 是第一次赋值
      isExpand: true,
      type: '', // add view edit
      sourceType: '', // 0： 手工创建  1：共享财务  2：商城
      logisticsMethodList: [
        { text: '海运', value: '1' },
        { text: '空运', value: '2' },
        { text: '铁运', value: '3' },
        { text: '干线', value: '5' }
      ],
      demandTypeList: [], // 需求类型
      templateCodeList: [],
      businessTypeList: [], // 业务类型
      railwayModeList: [], // 铁运模式
      applyUserIdData: [], // 申请人列表
      purchaserUserIdData: [], // 采购员列表
      organizationData: {},
      applyCompanyData: [], // 行政公司
      companyOptions: [], //业务公司
      applyDepartData: [], // 部门
      addForm: {
        processDocName: '', // 单据名词
        businessTypeId: '', // 业务类型 businessTypeCode | businessTypeName
        applyUserId: null, // 申请人 applyUserName | applyUserCode
        purchaserId: null, // 采购员 purchaserName | purchaserCode
        administrationCompanyId: null, //  行政公司administrationCompanyName | administrationCompanyCode
        // administrationCompanyName: null, //
        companyId: '', //业务公司 companyCode | companyName
        applyDepId: '', // 申请部门 applyDepCode | applyDepName
        remark: ''
      },
      rules: {
        demandType: [
          {
            required: true,
            message: this.$t('请选择需求类型'),
            trigger: 'blur'
          }
        ],
        templateCode: [
          {
            required: true,
            message: this.$t('请选择模板'),
            trigger: 'blur'
          }
        ],
        processDocName: [
          {
            required: true,
            message: this.$t('请填写名称'),
            trigger: 'blur'
          }
        ],
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        categoryId: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        logisticsMethodCode: [
          {
            required: true,
            message: this.$t('请选择物流方式'),
            trigger: 'blur'
          }
        ],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        purchaserId: [{ required: true, message: this.$t('请选择采购员'), trigger: 'blur' }],
        administrationCompanyId: [
          {
            required: true,
            message: this.$t('请选择行政公司'),
            trigger: 'blur'
          }
        ],
        companyId: [
          {
            required: true,
            message: this.$t('请选择业务公司'),
            trigger: 'blur'
          }
        ],
        applyDepId: [
          {
            required: true,
            message: this.$t('请选择申请部门'),
            trigger: 'blur'
          }
        ],
        railwayMode: [
          {
            required: true,
            message: this.$t('请选择铁运模式'),
            trigger: 'blur'
          }
        ]
      }
    }
  },

  computed: {
    // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
    canSave() {
      return this.$route.query.type !== 'view'
    },
    // !查看状态 && (状态为0或3 || 是 补充信息操作)
    canSubmit() {
      return (
        !['add', 'view'].includes(this.$route.query.type) &&
        ((this.headerInfo &&
          (this.headerInfo.requestStatus == 0 || this.headerInfo.requestStatus == 3)) ||
          this.type == 'replanish')
      )
    }
  },

  watch: {
    headerInfo: {
      handler(newVal) {
        if (!this.isInit) return
        this.addForm = {
          ...this.addForm,
          ...newVal
        }
        this.isInit = false
        this.$route.query.type != 'view' && this.getList()
      }
    },
    'addForm.companyId': {
      handler: function (newVal) {
        let params = {
          companyId: newVal
        }
        if (newVal && this.$refs.companyRef1) {
          this.$nextTick(() => {
            let _data = this.$refs.companyRef1.ejsRef.getDataByValue(newVal)
            if (_data) {
              params.companyName = _data.orgName
              params.companyCode = _data.orgCode
            }
            this.$store.commit('updatePrCompanyInfo', params)
          })
        } else {
          this.$store.commit('updatePrCompanyInfo', params)
        }
      }
    },
    'addForm.applyUserId': {
      handler(v) {
        if (this.sourceType != 1 && v !== '0') {
          this.getCompany() // 获取行政公司
        }
      }
    }
  },

  filters: {
    formatTime: (e) => {
      if (e && !isNaN(e) && e.length == 13) {
        e = Number(e)
        return timeNumberToDate({
          formatString: 'YYYY-mm-dd HH:MM:SS',
          value: e
        })
      } else {
        return '-'
      }
    }
  },

  mounted() {
    this.type = this.$route.query.type
    this.sourceType = this.$route.query.source
    this.getList()
    if (this.$route.query.type != 'view') {
      //引入mtech-common/utils中的防抖，(mtech-common/utils )
      this.getCompany = utils.debounce(this.getCompany, 300)
      this.getCompanyOptions = utils.debounce(this.getCompanyOptions, 300)
      this.getCompanyOptions({ text: '' })
    }
    // 获取当前登录的用户
    this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
  },

  methods: {
    //查询业务公司下拉数据
    getCompanyOptions(e = { text: '' }) {
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: [],
          fuzzyParam: e.text
        })
        .then((res) => {
          let companyOptions = res.data
          companyOptions.forEach((item) => {
            item.labelShow = item.orgCode + ' - ' + item.orgName
          })
          this.companyOptions = companyOptions

          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(companyOptions)
            }
          })
        })
    },
    // 初始化获取字典数据
    async initDictItems() {
      let codeList = [
        { code: 'LOGISTICS_DEMAND_TYPE', type: 'string' }, // 需求类型
        { code: 'LOGISTICS_POINT_RAILWAY_RAILWAYMODE', type: 'string' } // 铁运模式
      ]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          // 需求类型
          this.demandTypeList = res.data.LOGISTICS_DEMAND_TYPE
          // 铁运模式
          this.railwayModeList = res.data.LOGISTICS_POINT_RAILWAY_RAILWAYMODE
        }
      })
    },
    // 获取业务类型列表
    async getList() {
      this.initDictItems()
      if (!this.businessTypeList.length) {
        await this.$API.masterData
          .getDictCode({
            dictCode: 'businessType'
          })
          .then((res) => {
            this.businessTypeList = res.data
            if (res.code === 200) {
              const _obj = res.data.find((item) => item.itemCode === 'BTTCL006')
              this.addForm = {
                ...this.addForm,
                ...{
                  businessTypeCode: _obj.itemCode,
                  businessTypeId: _obj.id,
                  businessTypeName: _obj.itemName
                }
              }
            }
          })
      }

      // 获取 用户列表
      if (!this.applyUserIdData.length) {
        const params = {}
        if (this.headerInfo?.applyUserCode) {
          params.accountName = this.headerInfo.applyUserCode
        }
        await this.$API.masterData.getCurrentTenantEmployees(params).then((res) => {
          const tmp = []
          res.data?.forEach((item) => {
            tmp.push({
              ...item,
              text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
            })
          })
          this.applyUserIdData = tmp
        })
      }
      // 设置申请人默认值
      if (!this.headerInfo?.applyUserId) {
        this.addForm.applyUserId = this.applyUserIdData?.find(
          (item) => item.employeeId == this.userInfo?.employeeId
        )?.employeeId
      }
      // 获取 模板列表
      if (!this.templateCodeList.length) {
        await this.$API.bgConfig
          .getLogisticsTemplateConfig({
            page: { current: 1, size: 1000 },
            status: 1
          })
          .then((res) => {
            if (res.code === 200) {
              this.templateCodeList = res.data.records.map((item) => {
                item.templateId = item.id
                item.text = item.templateName
                item.value = item.templateCode
                return item
              })
            }
          })
      }

      this.getPurchaserUser({ text: this.addForm?.purchaserName })
    },
    // 获取 用户列表
    getUser(e) {
      const { text: fuzzyName } = e
      this.applyUserIdData = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.applyUserIdData = tmp
      })
    },
    // 获取 采购员用户列表
    getPurchaserUser(e) {
      const { text: fuzzyName } = e
      this.purchaserUserIdData = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.employeeCode}-${item.employeeName}`
          })
        })
        this.purchaserUserIdData = tmp
      })
    },

    // 获取当前用户下的行政公司
    getCompany(e = { text: '' }) {
      setTimeout(() => {
        if (!this.addForm.applyUserId) {
          return
        }
        this.$API.masterData
          .getOrgCompanysByEmpId({
            employeeId: this.addForm.applyUserId,
            fuzzyParam: e.text
          })
          .then((res) => {
            let applyCompanyData = res.data
            applyCompanyData.forEach((item) => {
              item.labelShow = item.orgCode + ' - ' + item.orgName
            })
            this.applyCompanyData = applyCompanyData
            this.$nextTick(() => {
              if (e.updateData && typeof e.updateData == 'function') {
                e.updateData(applyCompanyData)
              }
            })
            console.log('applyCompanyData', this.applyCompanyData)

            if (this.headerInfo.administrationCompanyId) {
              this.addForm.administrationCompanyId = this.headerInfo.administrationCompanyId
            } else if (this.applyCompanyData && this.applyCompanyData.length == 1) {
              this.addForm.administrationCompanyId = this.applyCompanyData[0].id
            } else if (this.applyCompanyData.length > 1) {
              this.addForm.administrationCompanyId = null
            }
          })
      }, 10)
    },
    //业务公司改变
    handleCompanyChange1(e) {
      this.$emit('companyChange')
      this.addForm.companyCode = e.itemData.orgCode
      this.addForm.companyName = e.itemData.orgName
    },
    // 行政公司改变
    handleCompanyChange() {
      this.getDepart()
    },
    handleSave() {
      this.$emit('save')
    },
    handleSubmit() {
      this.$emit('submit')
    },
    // 获取当前用户下公司 的 部门
    getDepart() {
      setTimeout(() => {
        if (!this.addForm.administrationCompanyId) {
          return
        }
        this.$API.masterData
          .getDepartmentsByEmpId({
            employeeId: this.addForm.applyUserId,
            companyOrganizationId: this.addForm.administrationCompanyId
          })
          .then((res) => {
            console.log('applyDepartData', this.applyDepartData)
            this.applyDepartData = res.data

            if (this.headerInfo.applyDepId) {
              this.addForm.applyDepId = this.headerInfo.applyDepId
            } else if (this.applyDepartData && this.applyDepartData.length == 1) {
              this.addForm.applyDepId = this.applyDepartData[0].id
            } else if (this.applyDepartData.length > 1) {
              this.addForm.applyDepId = null
            }
          })
      }, 10)
    },
    purchaserChange(e) {
      if (e && e.itemData) {
        sessionStorage.setItem(
          'prApplyPurchaserInfo',
          JSON.stringify({ ...e.itemData, businessTypeCode: this.addForm.businessTypeCode })
        )
      }
    },

    // 获取其他数据 code、name
    getOtherInfo(params) {
      // 需求类型
      if (this.addForm.demandType && this.$refs.demandTypeRef) {
        let _data = this.$refs.demandTypeRef.ejsRef.getDataByValue(this.addForm.demandType)
        if (!_data) return
        params.demandTypeName = _data.dictName
      }
      // 物流方式
      if (this.addForm.logisticsMethodCode && this.$refs.logisticsMethodRef) {
        let _data = this.$refs.logisticsMethodRef.ejsRef.getDataByValue(
          this.addForm.logisticsMethodCode
        )
        if (!_data) return
        params.logisticsMethodName = _data.text
      }
      // 模板
      if (this.addForm.templateCode && this.$refs.templateRef) {
        let _data = this.$refs.templateRef.ejsRef.getDataByValue(this.addForm.templateCode)
        if (!_data) return
        params.templateId = _data.templateId
        params.templateName = _data.text
      }

      // 采购
      if (this.addForm.purchaserId && this.$refs.purchaserRef) {
        let _data = this.$refs.purchaserRef.$refs.filterSelectRef.ejsRef.getDataByValue(
          this.addForm.purchaserId
        )
        if (!_data) return
        params.purchaserName = _data.employeeName
        params.purchaserCode = _data.accountName
      }
      // 申请人
      if (this.addForm.applyUserId && this.$refs.userRef) {
        let _data = this.$refs.userRef.$refs.filterSelectRef.ejsRef.getDataByValue(
          this.addForm.applyUserId
        )
        if (!_data) return
        params.applyUserName = _data.employeeName
        params.applyUserCode = _data.accountName
      }
      // 如果是共享财务推送的，行政公司和部门是带出的，不下拉选
      if (this.sourceType != 1) {
        if (this.addForm.administrationCompanyId && this.$refs.companyRef) {
          let _data = this.$refs.companyRef.ejsRef.getDataByValue(
            this.addForm.administrationCompanyId
          )
          if (!_data) return
          params.administrationCompanyName = _data.orgName
          params.administrationCompanyCode = _data.orgCode
        }

        if (this.addForm.applyDepId && this.$refs.depRef) {
          let _data = this.$refs.depRef.ejsRef.getDataByValue(this.addForm.applyDepId)
          if (!_data) return
          params.applyDepName = _data.orgName
          params.applyDepCode = _data.orgCode
        }
      }
      // 公司
      if (this.addForm.companyId && this.$refs.companyRef1) {
        let _data = this.$refs.companyRef1.ejsRef.getDataByValue(this.addForm.companyId)
        if (!_data) return
        params.companyName = _data.orgName
        params.companyCode = _data.orgCode
      }
      // 品类
      if (this.addForm.categoryId && this.$refs.categoryRef) {
        let _data = this.$refs.categoryRef.$refs.selectRef.ejsRef.getDataByValue(
          this.addForm.categoryId
        )
        if (!_data) return
        params.categoryCode = _data.categoryCode
        params.categoryName = _data.categoryName
      }
      // 铁运模式
      if (this.addForm.railwayMode && this.$refs.railwayModeRef) {
        let _data = this.$refs.railwayModeRef.ejsRef.getDataByValue(this.addForm.railwayMode)
        if (!_data) return
        params.railwayModeName = _data.dictName
      }
      return params
    },

    async confirm() {
      let res = 0
      await this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.addForm
          }
          params = this.getOtherInfo(params)
          res = params
        }
      })
      return res
    },

    goBack() {
      sessionStorage.removeItem('prApplyPurchaserInfo')
      this.$router.go(-1)
    }
  },
  destroyed() {
    sessionStorage.removeItem('prApplyPurchaserInfo')
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 0 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
