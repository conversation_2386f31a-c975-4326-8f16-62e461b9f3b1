<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="configGroupType" :label="$t('配置方式')">
        <mt-select
          v-model="ruleForm.configGroupType"
          :data-source="configGroupTypeOptions"
          @change="configGroupTypeClick"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')" v-if="ruleForm.configGroupType !== 5">
        <mt-select
          :open-dispatch-change="false"
          v-model="ruleForm.siteCode"
          :data-source="siteCodeOptions"
          :fields="{ text: 'label', value: 'siteCode' }"
          :placeholder="$t('请选择')"
          @change="siteCodeChange"
          :allow-filtering="true"
          :filtering="postSiteFuzzyQuery"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="itemCode" :label="$t('物料')" v-if="ruleForm.configGroupType === 4">
        <ItemCode
          @updateItemInfo="updateItemInfo"
          :item-code="ruleForm.itemCode"
          :item-name="ruleForm.itemName"
        ></ItemCode>
      </mt-form-item>
      <mt-form-item
        prop="siteAddressCode"
        :label="$t('库存地点')"
        v-if="
          ruleForm.configGroupType === 1 ||
          ruleForm.configGroupType === 3 ||
          ruleForm.configGroupType === 4 ||
          ruleForm.configGroupType === 7
        "
      >
        <mt-select
          v-model="ruleForm.siteAddressCode"
          :data-source="siteAddressCodeOptions"
          :fields="{ text: 'label', value: 'locationCode' }"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          @open="startOpen"
          :filtering="serchText"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="vmiWarehouseCode"
        :label="$t('VMI仓')"
        v-if="ruleForm.configGroupType === 6"
      >
        <mt-select
          v-model="ruleForm.vmiWarehouseCode"
          :data-source="vmiWarehouseCodeOptions"
          :disabled="type"
          :show-clear-button="false"
          :fields="{ text: 'label', value: 'vmiWarehouseCode' }"
          :placeholder="$t('请输入VMI编码或名称')"
          :filtering="getVmiWarehouse"
          :allow-filtering="true"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="subSiteName" :label="$t('分厂')" v-if="ruleForm.configGroupType === 3">
        <!-- <mt-select
          v-model="ruleForm.subSiteCode"
          :data-source="siteCodeOptions"
          :fields="{ text: 'label', value: 'siteCode' }"
          :placeholder="$t('请选择')"
          @change="siteCodeChange1"
        ></mt-select> -->
        <mt-input
          type="text"
          v-model="ruleForm.subSiteName"
          :placeholder="$t('请输入文本')"
          v-if="maxlength1"
          :maxlength="maxlength1"
        ></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="subSiteCode"
        :label="$t('分厂编码')"
        v-if="ruleForm.configGroupType === 3"
      >
        <mt-input
          type="text"
          v-model="ruleForm.subSiteCode"
          :placeholder="$t('请输入文本')"
          v-if="maxlength4"
          :maxlength="maxlength4"
        ></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="subSiteAddressName"
        :label="$t('分厂库存地点名称')"
        v-if="ruleForm.configGroupType === 3"
      >
        <!-- <mt-select
          v-model="ruleForm.subSiteAddressCode"
          :data-source="subSiteAddressCodeOptions"
          :fields="{ text: 'label', value: 'locationCode' }"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          :filtering="serchText1"
          @open="startOpen1"
          :open-dispatch-change="false"
        ></mt-select> -->
        <mt-input
          type="text"
          v-model="ruleForm.subSiteAddressName"
          :placeholder="$t('请输入文本')"
          v-if="maxlength1"
          :maxlength="maxlength1"
        ></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="subSiteAddressCode"
        :label="$t('分厂库存地点编码')"
        v-if="ruleForm.configGroupType === 3"
      >
        <mt-input
          type="text"
          v-model="ruleForm.subSiteAddressCode"
          :placeholder="$t('请输入文本')"
          v-if="maxlength4"
          :maxlength="maxlength4"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="categoryId" :label="$t('品类')" v-if="ruleForm.configGroupType === 7">
        <debounce-filter-select
          v-model="ruleForm.categoryCode"
          :request="getItemGroupList"
          :data-source="ItemGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'categoryCode' }"
          :value-template="ItemGroupTemplate"
          @change="ItemGroupChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item
        prop="supplierCode"
        :label="$t('加工商')"
        v-if="
          ruleForm.configGroupType === 2 ||
          ruleForm.configGroupType === 5 ||
          ruleForm.configGroupType === 1
        "
      >
        <mt-select
          v-model="ruleForm.supplierCode"
          :data-source="supplierOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          :fields="{ text: 'label', value: 'supplierCode' }"
          :filtering="serchText2"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="consigneeName" :label="$t('送货联系人')">
        <mt-input
          type="text"
          v-model="ruleForm.consigneeName"
          :placeholder="$t('请输入文本')"
          v-if="maxlength3"
          :maxlength="maxlength3"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="consigneePhone" :label="$t('送货联系电话')">
        <mt-inputNumber
          :width="400"
          :show-spin-button="false"
          v-model="ruleForm.consigneePhone"
          :placeholder="$t('请输入')"
        ></mt-inputNumber>
      </mt-form-item>
      <mt-form-item prop="consigneeAddress" :label="$t('送货地址')">
        <mt-input
          type="text"
          v-model="ruleForm.consigneeAddress"
          :placeholder="$t('请输入文本')"
          v-if="maxlength1"
          :maxlength="maxlength1"
        ></mt-input>
      </mt-form-item>
      <mt-form-item
        v-if="ruleForm.configGroupType !== 5"
        prop="consigneeAddressCode"
        :label="$t('送货地址编码')"
      >
        <mt-input
          type="text"
          v-model="ruleForm.consigneeAddressCode"
          :placeholder="$t('请输入文本')"
          v-if="maxlength4"
          :maxlength="maxlength4"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="needDefault" :label="$t('是否默认')">
        <mt-switch
          :active-value="1"
          :inactive-value="0"
          v-model="ruleForm.needDefault"
          :on-label="$t('是')"
          :off-label="$t('否')"
        ></mt-switch>
      </mt-form-item>
      <mt-form-item prop="relationItemExpend" :label="$t('关联物料消耗')">
        <mt-switch
          :active-value="1"
          :inactive-value="0"
          v-model="ruleForm.relationItemExpend"
          :on-label="$t('是')"
          :off-label="$t('否')"
        ></mt-switch>
      </mt-form-item>
      <mt-form-item prop="isDirectDeliver" :label="$t('是否直送')">
        <mt-switch
          :active-value="1"
          :inactive-value="0"
          v-model="ruleForm.isDirectDeliver"
          :on-label="$t('是')"
          :off-label="$t('否')"
        ></mt-switch>
      </mt-form-item>
      <mt-form-item prop="code" :label="$t('地址唯一编号')">
        <mt-input type="text" v-model="ruleForm.code" :disabled="true"></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { RegExpMap } from '@/utils/constant'
import ItemCode from './ItemCode.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    ItemCode,
    debounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    const { phoneNumReg } = RegExpMap
    const driverPhoneValidator = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!phoneNumReg.test(value)) {
        callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        this.$refs.ruleForm.clearValidate(['consigneePhone'])
        callback()
      }
    }
    const itemCodeValid = (rule, value, callback) => {
      console.log(this.ruleForm.itemCode, '我是选择的物料')
      if (!this.ruleForm.itemCode) {
        callback(new Error(this.$t('请选择物料')))
      } else {
        this.$refs.ruleForm.clearValidate(['itemCode'])
        callback()
      }
    }
    return {
      maxlength1: 128,
      ItemGroupOptions: '',

      maxlength3: 40,
      maxlength4: 50,
      configGroupTypeOptions: [
        { text: this.$t('工厂+库存地点'), value: 1 },
        { text: this.$t('工厂+加工商'), value: 2 },
        { text: this.$t('工厂+库存地点+分厂+分厂库存地点'), value: 3 },
        { text: this.$t('工厂+物料+库存地点'), value: 4 },
        { text: this.$t('加工商'), value: 5 },
        { text: this.$t('工厂+VMI仓'), value: 6 },
        { text: this.$t('工厂+库存地点+品类'), value: 7 }
      ], //配置
      siteCodeOptions: [], // 工厂 分厂 下拉
      siteAddressCodeOptions: [], // 库存地点 分厂库存地点下拉
      subSiteAddressCodeOptions: [], //分厂库存地点
      supplierOptions: [], //加工商下拉
      vmiWarehouseCodeOptions: [],
      dialogTitle: '',
      ItemGroupTemplate: codeNameColumn({
        firstKey: 'categoryCode',
        secondKey: 'categoryName'
      }), //
      rules: {
        configGroupType: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        subSiteName: [{ required: true, message: this.$t('请输入分厂'), trigger: 'blur' }],
        subSiteCode: [
          {
            required: true,
            message: this.$t('请输入分厂编码'),
            trigger: 'blur'
          }
        ],
        categoryId: [
          {
            required: true,
            message: this.$t('该项必填'),
            trigger: 'blur'
          }
        ],
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        itemCode: [
          {
            required: true,
            trigger: 'change',
            validate: itemCodeValid,
            message: this.$t('请选择物料')
          }
        ],
        consigneePhone: [
          {
            required: false,
            trigger: 'blur',
            validator: driverPhoneValidator
          }
        ],
        consigneeAddress: [
          {
            required: true,
            message: this.$t('请输入送货人地址'),
            trigger: 'blur'
          }
        ],
        supplierCode: [{ required: true, message: this.$t('请选择加工商'), trigger: 'blur' }],
        subSiteAddressCode: [
          {
            required: true,
            message: this.$t('请输入分厂库存地点编码'),
            trigger: 'blur'
          }
        ],
        subSiteAddressName: [
          {
            required: true,
            message: this.$t('请输入分厂库存地点名称'),
            trigger: 'blur'
          }
        ],
        siteAddressCode: [
          {
            required: true,
            message: this.$t('请选择库存地点'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseCode: [
          {
            required: true,
            message: this.$t('请选择VMI仓'),
            trigger: 'blur'
          }
        ],
        needDefault: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        isDirectDeliver: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        relationItemExpend: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        consigneeAddressCode: [
          {
            required: true,
            message: this.$t('请输入送货人地址编码'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        id: '',
        configGroupType: '', //配置组合方式
        siteCode: '', //工厂code
        status: 1,
        itemCode: '',
        categoryId: '',
        categoryName: '',
        categoryCode: '',
        itemName: '',
        subSiteCode: '', //分厂code
        subSiteName: '',
        subSiteAddressName: '',
        siteAddressCode: '', //库存地点code
        vmiWarehouseName: '', //VMI仓name
        vmiWarehouseCode: '', //VMI仓code
        subSiteAddressCode: '', //分厂库存地点code
        supplierCode: '', //供应商code
        consigneeName: '', //送货人姓名
        consigneePhone: '', //送货人电话
        consigneeAddress: '', //送货人地址
        consigneeAddressCode: '', //送货地址编码
        needDefault: 0, //是否默认
        isDirectDeliver: 0, //是否直送
        relationItemExpend: 0, //关联物料消耗
        code: '' // 地址唯一编号
      },
      show: false,
      entryInfo: {}
    }
  },
  mounted() {
    this.getOptions()
    // this.getItemGroupList({ text: "" });

    this.getLocation = utils.debounce(this.getLocation, 1000)
    this.postSiteFuzzyQuery = utils.debounce(this.postSiteFuzzyQuery, 1000)
    this.getSupplier = utils.debounce(this.getSupplier, 1000)
    this.getVmiWarehouse = utils.debounce(this.getVmiWarehouse, 1000)
  },
  methods: {
    ItemGroupChange(e) {
      const { itemData } = e

      this.ruleForm.categoryId = itemData.id
      this.ruleForm.categoryCode = itemData.categoryCode
      this.ruleForm.categoryName = itemData.categoryName
    },
    getItemGroupList(args, entryFirst) {
      const { text } = args
      const params = {
        fuzzyNameOrCode: text
        // groupTypeCode: "BG001CG",
      }
      this.$API.masterData
        .getCategoryfuzzy(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.ItemGroupOptions = addCodeNameKeyInList({
              firstKey: 'categoryCode',
              secondKey: 'categoryName',
              list
            })
            if (entryFirst === '1') {
              console.log(this.entryInfo, '123')
              this.ruleForm.categoryCode = this.entryInfo.row.categoryCode
              this.ruleForm.categoryId = this.entryInfo.row.categoryId
              this.ruleForm.categoryName = this.entryInfo.row.categoryName
            }
            // if (updateData && typeof updateData === "function") {
            //   this.$nextTick(() => {
            //     updateData(this.buyerOrgOptions);
            //   });
            // }
            // if (setSelectData) {
            //   this.$nextTick(() => {
            //     setSelectData();
            //   });
            // }
          }
        })
        .catch(() => {})
    },
    configGroupTypeClick(e) {
      console.log(e)
      if (e.value === 5) {
        this.ruleForm.consigneeAddressCode = 0
      }
      const supplierCodeRule = [
        { required: true, message: this.$t('请选择加工商'), trigger: 'blur' }
      ]
      if (e.value === 1) {
        this.rules.supplierCode = null
      } else {
        this.rules.supplierCode = supplierCodeRule
      }
    },
    updateItemInfo(val) {
      console.log(val, '我是徐福街道三')
      this.ruleForm.itemCode = val.itemCode
      this.ruleForm.itemName = val.itemName
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args, entryFirst) {
      const { text, updateData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeOptions = list.map((item) => {
              return {
                ...item,
                name: item.siteName,
                label: `${item.siteCode}-${item.siteName}`
              }
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteCodeOptions)
              })
            }
            if (entryFirst === '1') {
              this.ruleForm.siteCode = this.entryInfo.row.siteCode
            }
          }
        })
        .catch(() => {})
    },
    siteCodeChange1(val) {
      console.log('分厂改变', val)
      if (val.itemData?.siteCode) {
        // this.getLocation1("", val.itemData.siteCode);
      }
    },
    siteCodeChange(val) {
      if (val.itemData?.siteCode) {
        this.getLocation('', val.itemData.siteCode)
        this.getVmiWarehouse('', val.itemData?.siteCode)
      }
    },
    startOpen() {
      if (!this.siteAddressCodeOptions.length) {
        this.getLocation()
      }
    },
    startOpen1() {
      if (!this.subSiteAddressCodeOptions.length) {
        this.getLocation1()
      }
    },
    async serchText(val, type) {
      console.log('库存搜索值', val, type)
      await this.getLocation(val && val.text ? val.text : '', null, val)
    },
    serchText1(val, type) {
      console.log('分厂库存搜索值', val, type)
      // this.getLocation1(val && val.text ? val.text : "");
    },
    async getLocation(val, siteCode, e, entryFirst) {
      //查询库存地点
      console.log(e, '我是库存地点查询参数')
      let str = val || this.ruleForm.siteAddressCode
      const currentSiteCode = siteCode || this.ruleForm.siteCode

      if (!currentSiteCode) {
        console.log('工厂没有')
        return
      }

      // 当工厂编码为2547时，使用queryPvWarehouseList接口
      if (currentSiteCode === '2547') {
        const params = {
          page: {
            current: 1,
            size: 50
          },
          fuzzyParam: str || '' //搜索参数
        }

        await this.$API.masterData.queryPvWarehouseList(params).then((res) => {
          let list = res.data?.records || []
          list.forEach((item) => {
            // 将光伏仓库数据格式转换为与库存地点一致的格式
            item.locationCode = item.warehouseCode
            item.locationName = item.warehouseName
            item.label = `${item.warehouseCode}-${item.warehouseName}`
          })
          this.siteAddressCodeOptions = list
          if (e && e.updateData) {
            this.$nextTick(() => {
              e.updateData(this.siteAddressCodeOptions)
            })
          }
          if (entryFirst === '1') {
            this.ruleForm.siteAddressCode = this.entryInfo.row.siteAddressCode
          }
        })
      } else {
        // 其他工厂编码使用原有的getLocationFuzzyQuery接口
        const params = {
          commonCode: currentSiteCode, //工厂code
          dataLimit: 50,
          fuzzyParam: str || '' //搜索参数
        }

        await this.$API.masterData.getLocationFuzzyQuery(params).then((res) => {
          let list = res.data || []
          list.forEach((item) => {
            if (item.externalCode) {
              item.locationCode = item.externalCode
              if (item.externalName) {
                item.locationName = item.externalName
              }
            }
            item.label = `${item.locationCode}-${item.locationName}`
          })
          this.siteAddressCodeOptions = list
          if (e && e.updateData) {
            this.$nextTick(() => {
              e.updateData(this.siteAddressCodeOptions)
            })
          }
          if (entryFirst === '1') {
            this.ruleForm.siteAddressCode = this.entryInfo.row.siteAddressCode
          }
        })
      }
    },
    // 获取VMI仓数据
    async getVmiWarehouse(e = { text: '' }, siteCode) {
      let params = {
        defaultRules: [
          {
            field: 'rel.site_code',
            operator: 'equal',
            value: siteCode || this.ruleForm.siteCode
          },
          {
            field: 'base.vmi_warehouse_type',
            operator: 'in',
            value: [0, 1, 2]
          }
        ],
        condition: 'and'
      }
      if (e.text) {
        params = Object.assign({}, params, {
          rules: [
            {
              field: 'base.vmi_warehouse_code',
              condition: 'or',
              value: e.text,
              type: 'string',
              operator: 'contains'
            },
            {
              field: 'base.vmi_warehouse_name',
              condition: 'or',
              value: e.text,
              type: 'string',
              operator: 'contains'
            }
          ]
        })
      }
      await this.$API.supplierCoordination.getVmiWarehouse(params).then((res) => {
        if (res?.code == 200) {
          const list = res?.data || []
          list.forEach((item) => {
            item.label = `${item.vmiWarehouseCode}-${item.vmiWarehouseName}`
          })
          this.vmiWarehouseCodeOptions = list
        }
      })
    },
    getOptions() {},
    serchText2(val) {
      console.log('搜索值', val)
      this.getSupplier(val && val.text ? val.text : '')
    },
    getSupplier(val, entryFirst) {
      //查询供应商的数据
      let str = val || this.ruleForm.supplierCode
      let params = {
        fuzzyNameOrCode: str || ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.supplierCode}-${item.supplierName}`
        })
        this.supplierOptions = res.data || []
        if (entryFirst === '1') {
          this.ruleForm.supplierCode = this.entryInfo.row.supplierCode
        }
      })
    },
    // 初始化
    async dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('新增')) {
        this.ruleForm = {
          configGroupType: '', //配置组合方式
          siteCode: '', //工厂code
          status: 1,
          subSiteCode: '', //分厂code
          subSiteName: '',
          subSiteAddressName: '',
          siteAddressCode: '', //库存地点code
          subSiteAddressCode: '', //分厂库存地点code
          vmiWarehouseName: '', //VMI仓名称
          vmiWarehouseCode: '', //VMI仓code
          supplierCode: null, //供应商code
          consigneeName: '', //送货人姓名
          consigneePhone: '', //送货人电话
          categoryCode: '', //
          categoryName: '', //

          categoryId: '', //
          consigneeAddress: '', //送货人地址
          needDefault: 0, //是否默认
          isDirectDeliver: 0, //是否直送
          relationItemExpend: 0, //关联物料消耗
          consigneeAddressCode: '',
          itemCode: '',
          itemName: ''
        }
        this.getItemGroupList({ text: '' })

        this.postSiteFuzzyQuery({ text: undefined })
        this.getSupplier()
      }
      if (this.dialogTitle === this.$t('编辑')) {
        if (entryInfo.row.consigneePhone && entryInfo.row.consigneePhone.includes('**')) {
          this.$API.deliveryConfig
            .checkDeliveryConfigInfo({ key: entryInfo.row?.encryptMap?.consigneePhone || '' })
            .then((res) => {
              if (res && res.code === 200) {
                this.ruleForm.consigneePhone = res.data || ''
              }
            })
        }
        this.entryInfo = entryInfo
        console.log(entryInfo.row.supplierCode, '编辑数据')
        this.ruleForm.status = entryInfo.row.status
        this.ruleForm.status = entryInfo.row.status
        this.ruleForm.id = entryInfo.row.id
        this.ruleForm.configGroupType = entryInfo.row.configGroupType //配置组合方式
        this.ruleForm.siteCode = null //工厂code
        this.ruleForm.subSiteCode = entryInfo.row.subSiteCode //分厂code
        this.ruleForm.subSiteName = entryInfo.row.subSiteName
        this.ruleForm.vmiWarehouseName = entryInfo.row.vmiWarehouseName
        this.ruleForm.vmiWarehouseCode = entryInfo.row.vmiWarehouseCode
        this.ruleForm.subSiteAddressName = entryInfo.row.subSiteAddressName
        // this.ruleForm.siteAddressCode = entryInfo.row.siteAddressCode; //库存地点code
        this.ruleForm.subSiteAddressCode = entryInfo.row.subSiteAddressCode //分厂库存地点code
        this.ruleForm.supplierCode = null //供应商code
        this.ruleForm.supplierName = null //供应商code
        this.ruleForm.consigneeName = entryInfo.row.consigneeName //送货人姓名
        this.ruleForm.consigneePhone = entryInfo.row.consigneePhone //送货人电话
        this.getItemGroupList({ text: entryInfo.row.categoryCode }, '1')

        this.ruleForm.categoryCode = null //
        this.ruleForm.categoryName = null //
        this.ruleForm.categoryId = null //

        this.ruleForm.consigneeAddress = entryInfo.row.consigneeAddress //送货人地址
        this.ruleForm.needDefault = entryInfo.row.needDefault //是否默认
        this.ruleForm.isDirectDeliver = entryInfo.row.isDirectDeliver //是否直送
        this.ruleForm.consigneeAddressCode = entryInfo.row.consigneeAddressCode
        this.ruleForm.relationItemExpend = entryInfo.row.relationItemExpend //关联物料消耗
        this.ruleForm.itemCode = entryInfo.row.itemCode
        this.ruleForm.itemName = entryInfo.row.itemName
        this.ruleForm.code = entryInfo.row.code

        this.getSupplier(entryInfo.row.supplierCode, '1')
        this.postSiteFuzzyQuery({ text: entryInfo.row.siteCode }, '1')
        this.getLocation(entryInfo.row.siteAddressCode, entryInfo.row.siteCode, '', '1')
        // vmi仓下拉
        this.getVmiWarehouse('', entryInfo.row.siteCode)
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            configGroupType: this.ruleForm.configGroupType,
            consigneeAddress: this.ruleForm.consigneeAddress,
            consigneeName: this.ruleForm.consigneeName,
            consigneePhone: this.ruleForm.consigneePhone,
            categoryCode: this.ruleForm.categoryCode,
            categoryName: this.ruleForm.categoryName,
            categoryId: this.ruleForm.categoryId,

            needDefault: this.ruleForm.needDefault,
            isDirectDeliver: this.ruleForm.isDirectDeliver,
            relationItemExpend: this.ruleForm.relationItemExpend,
            siteAddress: this.ruleForm.siteAddressCode,
            siteAddressName: this.siteAddressCodeOptions.find((item) => {
              return item.locationCode === this.ruleForm.siteAddressCode
            })?.locationName,
            siteCode: this.ruleForm.siteCode,
            siteId: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.id,
            siteName: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.siteName,
            vmiWarehouseCode: this.ruleForm.vmiWarehouseCode,
            vmiWarehouseName: this.vmiWarehouseCodeOptions.find((item) => {
              return item.vmiWarehouseCode === this.ruleForm.vmiWarehouseCode
            })?.vmiWarehouseName,
            status: this.ruleForm.status,
            // subSiteAddressName: this.subSiteAddressCodeOptions.find((item) => {
            //   return item.locationCode === this.ruleForm.subSiteAddressCode;
            // })?.locationName,
            subSiteAddressName: this.ruleForm.subSiteAddressName,
            subSiteAddress: this.ruleForm.subSiteAddressCode,
            subSiteCode: this.ruleForm.subSiteCode,
            // subSiteName: this.siteCodeOptions.find((item) => {
            //   return item.siteCode === this.ruleForm.subSiteCode;
            // })?.siteName,
            subSiteName: this.ruleForm.subSiteName,
            supplierCode: this.ruleForm.supplierCode,
            supplierId: this.supplierOptions.find((item) => {
              return item.supplierCode === this.ruleForm.supplierCode
            })?.id,
            supplierName: this.supplierOptions.find((item) => {
              return item.supplierCode === this.ruleForm.supplierCode
            })?.supplierName,
            consigneeAddressCode: this.ruleForm.consigneeAddressCode,

            itemCode: this.ruleForm.itemCode,
            itemName: this.ruleForm.itemName
          }
          if (params.configGroupType === 1) {
            params.subSiteAddressName = ''
            params.subSiteAddress = ''
            params.subSiteCode = ''
            params.subSiteName = ''
            // params.supplierCode = ''
            // params.supplierId = ''
            // params.supplierName = ''
            params.itemCode = ''
            params.itemName = ''
          }
          if (params.configGroupType === 2) {
            params.subSiteAddressName = ''
            params.subSiteAddress = ''
            params.siteAddress = ''
            params.subSiteCode = ''
            params.subSiteName = ''
            params.itemCode = ''
            params.itemName = ''
          }
          if (params.configGroupType === 3) {
            params.supplierCode = ''
            params.supplierId = ''
            params.supplierName = ''
            params.itemCode = ''
            params.itemName = ''
          }
          if (params.configGroupType === 4) {
            params.subSiteAddressName = ''
            params.subSiteAddress = ''
            params.subSiteCode = ''
            params.subSiteName = ''
            params.supplierCode = ''
            params.supplierId = ''
            params.supplierName = ''
          }

          if (this.dialogTitle === this.$t('编辑')) {
            params.id = this.ruleForm.id
            this.$API.deliverySchedule.siteTenantExtendupdate(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
          if (this.dialogTitle === this.$t('新增')) {
            this.$API.deliverySchedule.siteTenantExtendsave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
