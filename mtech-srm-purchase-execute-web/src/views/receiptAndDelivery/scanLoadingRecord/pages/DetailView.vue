<!-- 扫描装车记录-明细视图 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="detailToolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="true"
      grid-id="6293a697-1871-418c-8ded-d06759293939"
      search-grid-id="ffd2f127-6de3-44f2-b042-844d5490126a"
      @search="handleSearch"
      @searchConditionChange="handleSearchConditionChange"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
      @pageChange="handlePageChange"
      @pageSizeChange="handlePageSizeChange"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getDetailSearchFormItems } from '../config/searchForm'
import { detailColumnData, detailToolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: [],
      columns: detailColumnData,
      detailToolbar,
      currentPage: 1,
      pageSize: 20
    }
  },
  computed: {
    searchConditions() {
      return getDetailSearchFormItems()
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    handleSearchConditionChange(eventData) {
      const { value, fieldName } = eventData
      switch (fieldName) {
        case 'createTime':
        case 'updateTime':
          this.handleDateTimeChange(value, fieldName)
          break
        default:
          this.searchForm[fieldName] = value
          break
      }
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchForm[`${field}S`] = null
        this.searchForm[`${field}E`] = null
        return
      }

      this.searchForm[`${field}S`] = this.getUnix(dayjs(e.startDate).format('YYYY-MM-DD 00:00:00'))
      this.searchForm[`${field}E`] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59'))
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearch({ searchForm }) {
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.currentPage = 1
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.getTableData()
    },
    async getTableData() {
      const params = {
        ...this.searchForm,
        page: {
          current: this.currentPage,
          size: this.pageSize
        }
      }
      this.$refs.commonListRef.setLoading(true)
      const res = await this.$API.receiptAndDelivery
        .getScanLoadingRecordDetailApi(params)
        .catch(() => this.$refs.commonListRef.setLoading(false))
      this.$refs.commonListRef.setLoading(false)
      if (res?.code === 200) {
        const records = res.data?.records || []
        const total = res.data?.total || 0
        this.$refs.commonListRef.setTableData(records)
        this.$refs.commonListRef.setPagination({
          total,
          current: this.currentPage,
          size: this.pageSize
        })
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(e) {
      try {
        e.loading = true
        const params = {
          ...this.searchForm,
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        }
        const res = await this.$API.receiptAndDelivery
          .exportScanLoadingRecordDetailApi(params)
          .finally(() => {
            e.loading = false
          })

        if (res) {
          const fileName =
            getHeadersFileName(res) || `扫描装车记录明细_${dayjs().format('YYYY-MM-DD')}.xlsx`
          download({ fileName, blob: res.data })
          this.$toast({ content: this.$t('导出成功'), type: 'success' })
        }
      } catch (error) {
        e.loading = false
        this.$toast({ content: error?.msg || this.$t('导出失败'), type: 'error' })
      }
    },
    handlePageChange(pageData) {
      this.currentPage = pageData.page
      this.pageSize = pageData.pageSize
      this.getTableData()
    },
    handlePageSizeChange(pageData) {
      this.currentPage = pageData.page
      this.pageSize = pageData.pageSize
      this.getTableData()
    }
  }
}
</script>
