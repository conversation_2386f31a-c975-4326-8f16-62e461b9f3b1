<!-- 扫描装车记录-列表视图 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="true"
      grid-id="cbe76817-6a40-4674-be67-24d52930430d"
      search-grid-id="13ce5e77-5a90-4e1f-b1c4-29ac73947135"
      @search="handleSearch"
      @searchConditionChange="handleSearchConditionChange"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
      @pageChange="handlePageChange"
      @pageSizeChange="handlePageSizeChange"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getListSearchFormItems } from '../config/searchForm'
import { listColumnData, toolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: [],
      columns: listColumnData,
      toolbar,
      currentPage: 1,
      pageSize: 20
    }
  },
  computed: {
    searchConditions() {
      return getListSearchFormItems()
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    handleSearchConditionChange(eventData) {
      const { value, fieldName } = eventData
      switch (fieldName) {
        case 'createTime':
        case 'updateTime':
          this.handleDateTimeChange(value, fieldName)
          break
        default:
          this.searchForm[fieldName] = value
          break
      }
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchForm[`${field}S`] = null
        this.searchForm[`${field}E`] = null
        return
      }

      this.searchForm[`${field}S`] = this.getUnix(dayjs(e.startDate).format('YYYY-MM-DD 00:00:00'))
      this.searchForm[`${field}E`] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59'))
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearch({ searchForm }) {
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.currentPage = 1
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.getTableData()
    },
    async getTableData() {
      const params = {
        ...this.searchForm,
        page: {
          current: this.currentPage,
          size: this.pageSize
        }
      }
      this.$refs.commonListRef.setLoading(true)
      const res = await this.$API.receiptAndDelivery
        .getScanLoadingRecordListApi(params)
        .catch(() => this.$refs.commonListRef.setLoading(false))
      this.$refs.commonListRef.setLoading(false)
      if (res?.code === 200) {
        const records = res.data?.records || []
        const total = res.data?.total || 0
        this.$refs.commonListRef.setTableData(records)
        this.$refs.commonListRef.setPagination({
          total,
          current: this.currentPage,
          size: this.pageSize
        })
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        syncTms: () => this.handleSyncTms(),
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleSyncTms() {
      const selectedRows = this.$refs.commonListRef.getSelectedRows()
      if (selectedRows.length === 0) {
        this.$toast({ content: this.$t('请选择要同步的数据'), type: 'warning' })
        return
      }

      try {
        const params = {
          ids: selectedRows.map((row) => row.id)
        }
        const res = await this.$API.receiptAndDelivery.syncTmsApi(params)

        if (res?.code === 200) {
          this.$toast({ content: this.$t('同步TMS系统成功'), type: 'success' })
          this.getTableData()
        } else {
          this.$toast({ content: res?.msg || this.$t('同步TMS系统失败'), type: 'error' })
        }
      } catch (error) {
        this.$toast({ content: error?.msg || this.$t('同步TMS系统失败'), type: 'error' })
      }
    },
    async handleExport(e) {
      try {
        e.loading = true
        const params = {
          ...this.searchForm,
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        }
        const res = await this.$API.receiptAndDelivery
          .exportScanLoadingRecordApi(params)
          .finally(() => {
            e.loading = false
          })

        if (res) {
          const fileName =
            getHeadersFileName(res) || `扫描装车记录_${dayjs().format('YYYY-MM-DD')}.xlsx`
          download({ fileName, blob: res.data })
          this.$toast({ content: this.$t('导出成功'), type: 'success' })
        }
      } catch (error) {
        e.loading = false
        this.$toast({ content: error?.msg || this.$t('导出失败'), type: 'error' })
      }
    },
    handlePageChange(pageData) {
      this.currentPage = pageData.page
      this.pageSize = pageData.pageSize
      this.getTableData()
    },
    handlePageSizeChange(pageData) {
      this.currentPage = pageData.page
      this.pageSize = pageData.pageSize
      this.getTableData()
    }
  }
}
</script>
