import { i18n } from '@/main.js'
import { statusOptions, syncTmsStatusOptions } from './index'

// 列表视图搜索表单配置
export const getListSearchFormItems = () => [
  {
    label: i18n.t('装车单号'),
    field: 'loadCarOrderNo',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('送货单号'),
    field: 'deliveryCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('状态'),
    field: 'statusList',
    type: 'multiSelect',
    options: statusOptions
  },
  {
    label: i18n.t('供应商编码'),
    field: 'supplierCodeList',
    type: 'remoteAutocomplete',
    props: {
      url: '/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope',
      multiple: true,
      fields: { text: 'supplierName', value: 'supplierCode' },
      searchFields: ['supplierName', 'supplierCode']
    }
  },
  {
    label: i18n.t('供应商名称'),
    field: 'supplierName',
    type: 'input'
  },
  {
    label: i18n.t('车牌号'),
    field: 'vehicleNo',
    type: 'input'
  },
  {
    label: i18n.t('车型'),
    field: 'truckType',
    type: 'input'
  },
  {
    label: i18n.t('司机姓名'),
    field: 'driverName',
    type: 'input'
  },
  {
    label: i18n.t('司机手机号'),
    field: 'driverMobileNo',
    type: 'input'
  },
  {
    label: i18n.t('工厂代码'),
    field: 'siteCodeList',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      multiple: true,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('工厂名称'),
    field: 'siteName',
    type: 'input'
  },
  {
    label: i18n.t('收货人'),
    field: 'consignee',
    type: 'input'
  },
  {
    label: i18n.t('收货人联系方式'),
    field: 'contactWay',
    type: 'input'
  },
  {
    label: i18n.t('收货地址'),
    field: 'deliverAddr',
    type: 'input'
  },
  {
    label: i18n.t('同步TMS状态'),
    field: 'syncTmsStatus',
    type: 'select',
    options: syncTmsStatusOptions
  },
  {
    label: i18n.t('创建时间'),
    field: 'createTime',
    type: 'dateRange'
  },
  {
    label: i18n.t('最后修改时间'),
    field: 'updateTime',
    type: 'dateRange'
  }
]

// 明细视图搜索表单配置
export const getDetailSearchFormItems = () => [
  {
    label: i18n.t('装车单号'),
    field: 'loadCarOrderNo',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('送货单号'),
    field: 'deliveryCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('状态'),
    field: 'statusList',
    type: 'multiSelect',
    options: statusOptions
  },
  {
    label: i18n.t('供应商编码'),
    field: 'supplierCodeList',
    type: 'remoteAutocomplete',
    props: {
      url: '/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope',
      multiple: true,
      fields: { text: 'supplierName', value: 'supplierCode' },
      searchFields: ['supplierName', 'supplierCode']
    }
  },
  {
    label: i18n.t('供应商名称'),
    field: 'supplierName',
    type: 'input'
  },
  {
    label: i18n.t('车牌号'),
    field: 'vehicleNo',
    type: 'input'
  },
  {
    label: i18n.t('车型'),
    field: 'truckType',
    type: 'input'
  },
  {
    label: i18n.t('司机姓名'),
    field: 'driverName',
    type: 'input'
  },
  {
    label: i18n.t('司机手机号'),
    field: 'driverMobileNo',
    type: 'input'
  },
  {
    label: i18n.t('工厂代码'),
    field: 'siteCodeList',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      multiple: true,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('工厂名称'),
    field: 'siteName',
    type: 'input'
  },
  {
    label: i18n.t('物料编码'),
    field: 'materialCode',
    type: 'input'
  },
  {
    label: i18n.t('物料名称'),
    field: 'materialName',
    type: 'input'
  },
  {
    label: i18n.t('收货人'),
    field: 'consignee',
    type: 'input'
  },
  {
    label: i18n.t('收货人联系方式'),
    field: 'contactWay',
    type: 'input'
  },
  {
    label: i18n.t('收货地址'),
    field: 'deliverAddr',
    type: 'input'
  },
  {
    label: i18n.t('同步TMS状态'),
    field: 'syncTmsStatus',
    type: 'select',
    options: syncTmsStatusOptions
  },
  {
    label: i18n.t('创建时间'),
    field: 'createTime',
    type: 'dateRange'
  },
  {
    label: i18n.t('最后修改时间'),
    field: 'updateTime',
    type: 'dateRange'
  }
]
