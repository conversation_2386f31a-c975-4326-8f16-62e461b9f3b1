import { i18n } from '@/main.js'

// 状态选项
export const statusOptions = [
  { text: i18n.t('新建'), value: 1 },
  { text: i18n.t('发货中'), value: 2 },
  { text: i18n.t('已完成'), value: 3 },
  { text: i18n.t('已取消'), value: 4 },
  { text: i18n.t('已关闭'), value: 5 }
]

// 同步TMS状态选项
export const syncTmsStatusOptions = [
  { text: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步成功'), value: 1 },
  { text: i18n.t('同步失败'), value: 2 }
]

// 工具栏按钮配置
export const toolbar = [
  {
    code: 'syncTms',
    name: i18n.t('同步TMS系统'),
    icon: '',
    status: 'info',
    loading: false,
    permission: ['O_02_1886']
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false,
    permission: ['O_02_1887']
  }
]

export const detailToolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false,
    permission: ['O_02_1888']
  }
]

// 列表视图列配置
export const listColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('装车单号'),
    field: 'loadCarOrderNo',
    minWidth: 180,
    fixed: 'left'
  },
  {
    title: i18n.t('送货单号'),
    field: 'deliveryCode',
    minWidth: 150
  },
  {
    title: i18n.t('状态'),
    field: 'status',
    minWidth: 80,
    formatter: ({ cellValue }) => {
      return statusOptions.find((item) => item.value === cellValue)?.text || ''
    }
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 120
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 150
  },
  {
    title: i18n.t('车牌号'),
    field: 'vehicleNo'
  },
  {
    title: i18n.t('车型'),
    field: 'truckType'
  },
  {
    title: i18n.t('空车重(KG)'),
    field: 'vehicleWeight',
    minWidth: 120
  },
  {
    title: i18n.t('司机姓名'),
    field: 'driverName'
  },
  {
    title: i18n.t('司机手机号'),
    field: 'driverMobileNo',
    minWidth: 120
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    minWidth: 150
  },
  {
    title: i18n.t('卡板数量'),
    field: 'cardBoardQty'
  },
  {
    title: i18n.t('总箱数'),
    field: 'totalBoxQty'
  },
  {
    title: i18n.t('总送货数量'),
    field: 'totalDriverQty',
    minWidth: 120
  },
  {
    title: i18n.t('收货人'),
    field: 'consignee'
  },
  {
    title: i18n.t('收货人联系方式'),
    field: 'contactWay',
    minWidth: 130
  },
  {
    title: i18n.t('收货地址'),
    field: 'deliverAddr',
    minWidth: 200
  },
  {
    title: i18n.t('同步TMS状态'),
    field: 'syncTmsStatus',
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return syncTmsStatusOptions.find((item) => item.value === cellValue)?.text || ''
    }
  },
  {
    title: i18n.t('同步TMS接口信息'),
    field: 'syncTmsDesc',
    minWidth: 150
  },
  {
    title: i18n.t('创建人'),
    field: 'createUserName'
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime',
    minWidth: 160
  },
  {
    title: i18n.t('更新人'),
    field: 'updateUserName'
  },
  {
    title: i18n.t('更新时间'),
    field: 'updateTime',
    minWidth: 160
  }
]

// 明细视图列配置
export const detailColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('装车单号'),
    field: 'loadCarOrderNo',
    minWidth: 180,
    fixed: 'left'
  },
  {
    title: i18n.t('送货单号'),
    field: 'deliveryCode',
    minWidth: 150
  },
  {
    title: i18n.t('状态'),
    field: 'status',
    minWidth: 80,
    formatter: ({ cellValue }) => {
      return statusOptions.find((item) => item.value === cellValue)?.text || ''
    }
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 120
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 150
  },
  {
    title: i18n.t('车牌号'),
    field: 'vehicleNo'
  },
  {
    title: i18n.t('车型'),
    field: 'truckType'
  },
  {
    title: i18n.t('空车重(KG)'),
    field: 'vehicleWeight',
    minWidth: 120
  },
  {
    title: i18n.t('司机姓名'),
    field: 'driverName'
  },
  {
    title: i18n.t('司机手机号'),
    field: 'driverMobileNo',
    minWidth: 120
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    minWidth: 100
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    minWidth: 150
  },
  {
    title: i18n.t('物料编码'),
    field: 'materialCode',
    minWidth: 120
  },
  {
    title: i18n.t('物料名称'),
    field: 'materialName',
    minWidth: 150
  },
  {
    title: i18n.t('卡板数量'),
    field: 'cardBoardQty'
  },
  {
    title: i18n.t('总箱数'),
    field: 'totalBoxQty'
  },
  {
    title: i18n.t('总送货数量'),
    field: 'totalDriverQty',
    minWidth: 120
  },
  {
    title: i18n.t('收货人'),
    field: 'consignee'
  },
  {
    title: i18n.t('收货人联系方式'),
    field: 'contactWay',
    minWidth: 130
  },
  {
    title: i18n.t('收货地址'),
    field: 'deliverAddr',
    minWidth: 200
  },
  {
    title: i18n.t('BOXID'),
    field: 'boxId',
    minWidth: 120
  },
  {
    title: i18n.t('装箱数量'),
    field: 'packingQuantity'
  },
  {
    title: i18n.t('长(CM)'),
    field: 'boxBodyLength',
    minWidth: 80
  },
  {
    title: i18n.t('宽(CM)'),
    field: 'boxBodyWide',
    minWidth: 80
  },
  {
    title: i18n.t('高(CM)'),
    field: 'boxBodyHeight',
    minWidth: 80
  },
  {
    title: i18n.t('毛重(KG)'),
    field: 'grossWeight'
  },
  {
    title: i18n.t('净重(KG)'),
    field: 'netWeight'
  },
  {
    title: i18n.t('销售订单号'),
    field: 'saleOrderCode',
    minWidth: 120
  },
  {
    title: i18n.t('条码层级'),
    field: 'barcodeLevel'
  },
  {
    title: i18n.t('生产日期'),
    field: 'produceDate'
  },
  {
    title: i18n.t('采购组编码'),
    field: 'buyerOrgCode',
    minWidth: 120
  },
  {
    title: i18n.t('采购组名称'),
    field: 'buyerOrgName',
    minWidth: 150
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode',
    minWidth: 100
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName',
    minWidth: 150
  },
  {
    title: i18n.t('同步TMS状态'),
    field: 'syncTmsStatus',
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return syncTmsStatusOptions.find((item) => item.value === cellValue)?.text || ''
    }
  },
  {
    title: i18n.t('同步TMS接口信息'),
    field: 'syncTmsDesc',
    minWidth: 150
  },
  {
    title: i18n.t('创建人'),
    field: 'createUserName'
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime',
    minWidth: 160
  },
  {
    title: i18n.t('更新人'),
    field: 'updateUserName'
  },
  {
    title: i18n.t('更新时间'),
    field: 'updateTime',
    minWidth: 160
  }
]
