import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { ComponentChangeType, ComponentType } from './constant'
import { rowDataTemp } from './variable'
import { cloneDeep } from 'lodash'
import { numberInputOnKeyDown } from '@/utils/utils'

export const ColumnComponent = {
  // 文本 下拉选择时，带出的数据
  changedText: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('changedInputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content"><span>{{data[dataKey]}}</span></div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              ComponentChangeType
            }
          },
          beforeDestroy() {
            this.$bus.$off('reservationDeliverSupplierRowSearchDialogConfirm')
          },
          mounted() {
            // 监听变化
            this.onComponentChange()
          },
          methods: {
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (e) => {
                const { modifiedKeys, changeType, data } = e
                if (
                  modifiedKeys.includes(this.dataKey) &&
                  changeType === ComponentChangeType.code
                ) {
                  // 发布事件的数据修改了，关联值修改
                  this.data[dataKey] = data[this.dataKey]
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 文本 可编辑 关联
  inputChange: (args) => {
    const { dataKey, showClearBtn, maxlength } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-input
                v-model="data[dataKey]"
                :show-clear-button="showClearBtn"
                :disabled="false"
                :maxlength="maxlength"
                @input="onInput"
              ></mt-input>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              showClearBtn,
              maxlength
            }
          },
          mounted() {
            if (['driverPhone', 'driverIdNo'].includes(dataKey)) {
              if (this.data.encryptMap) {
                this.$API.deliveryConfig
                  .checkDeliveryConfigInfo({
                    key: this.data.encryptMap[dataKey] || ''
                  })
                  .then((res) => {
                    if (res && res.code === 200) {
                      this.data[dataKey] = res.data || ''
                      rowDataTemp[rowDataTemp.length - 1][dataKey] = res.data || ''
                    }
                  })
              }
            }
            // 监听变化
            this.onComponentChange()
          },
          beforeDestroy() {
            this.$bus.$off('reservationDeliverSupplierRowSearchDialogConfirm')
          },
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (e) => {
                const { modifiedKeys, data } = e
                if (modifiedKeys.includes(this.dataKey)) {
                  // 发布事件的数据修改了，关联值修改
                  const newData = cloneDeep(data[this.dataKey])
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 多选 显示
  multiSelect: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('multiSelectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div class="input-search-content">
                <mt-multi-select
                  v-model="data[dataKey]"
                  :data-source="selectOptions"
                  :disabled="true"
                ></mt-multi-select>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              ddd: [],
              selectOptions: []
            }
          },
          mounted() {
            // 将值转为下拉组件的 data-source
            this.dataToSelectOptions()
          },
          methods: {
            // 将值转为下拉组件的 data-source
            dataToSelectOptions() {
              const tmp = []
              if (this.data[dataKey] && this.data[dataKey].length) {
                this.data[dataKey].forEach((item) => {
                  tmp.push({
                    text: item,
                    value: item
                  })
                })
              }
              this.selectOptions = tmp
            }
          }
        })
      }
    }
    return template
  },
  // 多选 编辑 搜索
  multiSelectSearch: (args) => {
    const { dataKey, showClearBtn, disabled, showSearchBtn } = args
    const template = () => {
      return {
        template: Vue.component('multiSelectSearchComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div v-if="isShow" class="input-search-content">
                <mt-multi-select
                  v-model="data[dataKey]"
                  :data-source="selectOptions"
                  :show-clear-button="showClearBtn"
                  :disabled="disabled"
                  @change="onChange"
                ></mt-multi-select>
                <mt-icon
                  v-if="showSearchBtn"
                  name="icon_input_search"
                  @click.native="handleSearch"
                ></mt-icon>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              ddd: [],
              disabled,
              showClearBtn,
              showSearchBtn,
              selectOptions: [],
              isShow: true
            }
          },
          mounted() {
            this.$bus.$on('forecastTypeColumnChange', (args) => {
              const { data, requestKey } = args
              if (requestKey === 'forecastType') {
                if (data.value === 2) {
                  this.isShow = false
                } else {
                  this.isShow = true
                }
              }
            })
            // 将值转为下拉组件的 data-source
            this.dataToSelectOptions()
            // 监听 行数据编辑弹框的确认事件
            this.onRowSearchDialogConfirm()
          },
          beforeDestroy() {
            this.$bus.$off('rowSearchDialogConfirm')
            this.$bus.$off('forecastTypeColumnChange')
          },
          methods: {
            onChange(e) {
              if (e.e) {
                // 编辑行时改变rowDataTemp中当前的值
                rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
                this.data[dataKey] = e.value
              }
            },
            // 点击查询
            handleSearch() {
              this.$parent.$emit('handleSearch', {
                dataKey,
                data: rowDataTemp[rowDataTemp.length - 1]
              })
            },
            // 将值转为下拉组件的 data-source
            dataToSelectOptions() {
              const tmp = []
              if (this.data[dataKey] && this.data[dataKey].length) {
                this.data[dataKey].forEach((item) => {
                  tmp.push({
                    text: item,
                    value: item
                  })
                })
              }
              this.selectOptions = tmp
            },
            // 监听 行数据编辑弹框的确认事件
            onRowSearchDialogConfirm() {
              this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (e) => {
                const { data, modifiedKeys } = e
                if (modifiedKeys.includes(this.dataKey)) {
                  // 添加 行数据编辑弹框的确认事件 的数据
                  const list = cloneDeep(data[this.dataKey])
                  const newData =
                    list.length > 0 ? [...new Set(this.data[dataKey].concat(list))] : []
                  this.addDataByDialogConfirm(newData)
                  // 值修改
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData
                }
              })
            },
            // 添加 行数据编辑弹框的确认事件 的数据
            addDataByDialogConfirm(data) {
              const tmp = []
              data.forEach((item) => {
                tmp.push({
                  text: item,
                  value: item
                })
              })
              this.selectOptions = tmp
            }
          }
        })
      }
    }
    return template
  },
  // 文本 编辑 搜索
  inputSearch: (args) => {
    const { dataKey, showClearBtn, disabled, maxlength } = args
    const template = () => {
      return {
        template: Vue.component('inputSearchComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div class="input-search-content">
                <mt-input
                  v-model="data[dataKey]"
                  :show-clear-button="showClearBtn"
                  :disabled="disabled"
                  :maxlength="maxlength"
                  @input="onInput"
                ></mt-input>
                <mt-icon
                  name="icon_input_search"
                  @click.native="handleSearch"
                ></mt-icon>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              disabled,
              maxlength,
              showClearBtn
            }
          },
          mounted() {
            // 监听变化
            this.onComponentChange()
          },
          beforeDestroy() {
            this.$bus.$off('reservationDeliverSupplierRowSearchDialogConfirm')
          },
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            },
            handleSearch() {
              this.$parent.$emit('handleSearch', {
                dataKey,
                data: rowDataTemp[rowDataTemp.length - 1]
              })
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (e) => {
                const { modifiedKeys, data } = e
                if (modifiedKeys.includes(this.dataKey)) {
                  // 发布事件的数据修改了，关联值修改
                  const newData = cloneDeep(data[this.dataKey])
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 时间 搜索
  timeSearch: (args) => {
    const { dataKey, isDate, isTime, isDateTime } = args

    const template = () => {
      return {
        template: Vue.component('timeSearchComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div class="input-search-content">
                <div>
                  <span v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</span>
                  <span v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</span>
                </div>
                <mt-icon
                  name="icon_input_search"
                  @click.native="handleSearch"
                ></mt-icon>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              isDate,
              isTime,
              isDateTime
            }
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
              } else {
                str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'HH:MM:SS', value })
              } else {
                str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
              }

              return str
            }
          },
          mounted() {
            // 监听变化
            this.onComponentChange()
          },
          beforeDestroy() {
            this.$bus.$off('reservationDeliverSupplierRowSearchDialogConfirm')
          },
          methods: {
            handleSearch() {
              this.$parent.$emit('handleSearch', {
                dataKey,
                data: rowDataTemp[rowDataTemp.length - 1]
              })
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (e) => {
                const { modifiedKeys, data, requestKey } = e
                if (modifiedKeys.includes(this.dataKey)) {
                  // 发布事件的数据修改了，关联值修改
                  const newData = cloneDeep(data[this.dataKey])
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData
                }
                if (requestKey === 'forecastTime') {
                  // 预约时间选择了，带出装车能力
                  this.data.carAbility = data.carAbility
                  rowDataTemp[rowDataTemp.length - 1].carAbility = data.carAbility
                }
              })
            }
          }
        })
      }
    }

    return template
  },
  // 数字 编辑
  number: (args) => {
    const { dataKey, showClearBtn, precision, maxValue, minValue } = args
    const template = () => {
      return {
        template: Vue.component('numberComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-input-number
                @keydown.native="numberInputOnKeyDown"
                v-model="data[dataKey]"
                :max="maxValue"
                :min="minValue"
                :precision="precision"
                :show-spin-button="false"
                :show-clear-button="showClearBtn"
                @input="onInput"
              ></mt-input-number>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              maxValue,
              minValue,
              showClearBtn,
              numberInputOnKeyDown,
              precision: precision || 2
            }
          },
          mounted() {},
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 空的显示
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  // 不可编辑的文字显示
  text: (args) => {
    const { dataKey, cellTools } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content"><span>{{data[dataKey]}}</span></div>
            <div class="column-tool mt-flex invite-btn" v-if="haveShowCellTool">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                v-permission="cellTool.permission"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return { data: {}, dataKey, cellTools, haveShowCellTool: false }
          },
          mounted() {
            if (cellTools?.length > 0) {
              for (let i = 0; i < cellTools.length; i++) {
                const cellTool = cellTools[i]
                if (!cellTool.visibleCondition || cellTool.visibleCondition(this.data)) {
                  this.haveShowCellTool = true
                  break
                }
              }
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  },
  // 不可编辑的文字 搜索
  textSearch: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div class="input-search-content">
                <div>{{data[dataKey]}}</div>
                <mt-icon
                  name="icon_input_search"
                  @click.native="handleSearch"
                ></mt-icon>
              </div>
            </div>
          </div>`,
          data: function () {
            return { data: {}, dataKey }
          },
          mounted() {
            // 监听变化
            this.onComponentChange()
          },
          beforeDestroy() {
            this.$bus.$off('reservationDeliverSupplierRowSearchDialogConfirm')
          },
          methods: {
            handleSearch() {
              this.$parent.$emit('handleSearch', {
                dataKey,
                data: rowDataTemp[rowDataTemp.length - 1]
              })
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (e) => {
                const { modifiedKeys, data, requestKey } = e
                if (modifiedKeys.includes(this.dataKey)) {
                  // 发布事件的数据修改了，关联值修改
                  const newData = data[this.dataKey]
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData
                }
                if (requestKey === 'forecastTime') {
                  // 预约时间选择了，带出装车能力
                  this.data.carAbility = data.carAbility
                  rowDataTemp[rowDataTemp.length - 1].carAbility = data.carAbility
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 时间显示
  timeDate: (args) => {
    const { dataKey, isDateTime, isDate, isTime } = args

    const template = () => {
      return {
        template: Vue.component('timeDateComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</span>
              <span v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</span>
            </div>
          </div>`,
          data: function () {
            return { data: {}, dataKey, isDateTime, isDate, isTime }
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
              } else {
                str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'HH:MM:SS', value })
              } else {
                str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // 状态
  status: (args) => {
    const { type, dataKey, cellTools, statusClass, statusText } = args
    const template = () => {
      return {
        // 按钮：发布，取消发布，删除，确认
        template: Vue.component('statusComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"
          >
            <div class="field-content"><span :class="[statusClass[data[dataKey]]]">{{data[dataKey] | dataFormat}}</span></div>
            <div class="column-tool mt-flex invite-btn" v-if="type === ComponentType.view">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              type,
              dataKey,
              statusClass,
              statusText,
              cellTools,
              ComponentType
            }
          },
          filters: {
            // 值转换
            dataFormat(value) {
              let result = value
              if (statusText[value]) {
                result = statusText[value]
              }
              return result
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  },
  // list 信息 显示 点击出弹框编辑
  listInfoSearch: (args) => {
    const { dataKey, requestKey, isShowLength, infoKey, delimiter, type, isShowBtn } = args

    const template = () => {
      return {
        template: Vue.component('date', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div class="input-search-content">
                <span v-if="isShowLength">{{data[dataKey] && data[dataKey].length || '-' }}</span>
                <div v-else :class="[isShowBtn && type === ComponentType.edit && 'text-ellipsis']"><span v-for="(item, index) in data[dataKey]" :key="index">{{infoKey ? item[infoKey] : item}}{{data[dataKey].length != index + 1 ? delimiter : ''}}</span></div>
                <mt-icon
                  v-if="isShowBtn && type === ComponentType.edit"
                  name="icon_input_search"
                  @click.native="handleSearch"
                ></mt-icon>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              isShowLength,
              infoKey,
              delimiter,
              isShowBtn,
              type,
              ComponentType
            }
          },
          mounted() {
            if (type === ComponentType.edit) {
              // 监听变化
              this.onComponentChange()
            }
          },
          beforeDestroy() {
            if (type === ComponentType.edit) {
              this.$bus.$off('reservationDeliverSupplierRowSearchDialogConfirm')
            }
          },
          filters: {},
          methods: {
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (e) => {
                const { modifiedKeys, data, requestKey } = e
                if (modifiedKeys.includes(this.dataKey)) {
                  // 发布事件的数据修改了，关联值修改
                  const newData = cloneDeep(data[this.dataKey])
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData
                }
                if (requestKey === 'deliveryCode') {
                  // 勾选ASN时带出采方租户id
                  rowDataTemp[rowDataTemp.length - 1].purTenantId = data.purTenantId
                }
              })
            },
            handleSearch() {
              this.$parent.$emit('handleSearch', {
                dataKey: requestKey,
                data: rowDataTemp[rowDataTemp.length - 1]
              })
            }
          }
        })
      }
    }

    return template
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  },
  // 表格显示 code+name 监听变变化
  codeNameColumn: (args) => {
    const { firstKey, secondKey, type, dataKey } = args
    const template = () => {
      return {
        template: Vue.component('codeNameColumnComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span>{{theColumnData}}</span>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              firstKey,
              secondKey,
              theColumnData: '',
              dataKey
            }
          },
          mounted() {
            // 设置数据格子的值
            this.setTheColumnData(this.data)
            if (type === ComponentType.edit) {
              // 监听变化
              this.onComponentChange()
            }
          },
          beforeDestroy() {
            if (type === ComponentType.edit) {
              this.$bus.$off('reservationDeliverSupplierRowSearchDialogConfirm')
            }
          },
          methods: {
            // 设置数据格子的值
            setTheColumnData(data) {
              // 其一为空，则仅显示其一
              if (data[firstKey] && data[secondKey]) {
                // firstKey-secondKey
                this.theColumnData = `${data[firstKey]}-${data[secondKey]}`
              } else if (data[firstKey]) {
                // firstKey
                this.theColumnData = data[firstKey]
              } else if (data[secondKey]) {
                // secondKey
                this.theColumnData = data[secondKey]
              } else {
                // ""
                this.theColumnData = ''
              }
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (e) => {
                const { modifiedKeys, data } = e
                if (modifiedKeys.includes(dataKey)) {
                  // 发布事件的数据修改了，关联值修改
                  const newFirstKeyData = data[firstKey]
                  const newSecondKeyData = data[secondKey]
                  const newData = {
                    [firstKey]: newFirstKeyData,
                    [secondKey]: newSecondKeyData
                  }
                  // 设置数据格子的值
                  this.setTheColumnData(newData)
                  // 设置 rowDataTemp
                  rowDataTemp[rowDataTemp.length - 1][firstKey] = newFirstKeyData
                  rowDataTemp[rowDataTemp.length - 1][secondKey] = newSecondKeyData
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 下拉框 编辑
  select: (args) => {
    const {
      dataKey,
      selectOptions,
      fields,
      allowFiltering,
      showClearBtn,
      modifiedKeys,
      modifiedRelation
    } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                @change="selectChange"
                v-model="value"
                :allow-filtering="allowFiltering"
                :data-source="selectOptions"
                :show-clear-button="showClearBtn"
                :placeholder="$t('请选择')"
                :fields="fields"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              value: null,
              selectOptions,
              fields,
              dataKey,
              allowFiltering,
              showClearBtn,
              ComponentChangeType,
              flag: true
            }
          },
          mounted() {
            const defaultValue = this.$route.params?.sourceType
            this.value = defaultValue
            if (dataKey === 'forecastType') {
              this.value = rowDataTemp[rowDataTemp.length - 1]['forecastType'] || defaultValue
            }
            if ([0, 1].includes(defaultValue)) this.flag = false
          },
          methods: {
            // 修改中的值
            selectChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
              let args = {
                requestKey: dataKey,
                data: e.itemData
              }
              this.$bus.$emit('forecastTypeColumnChange', args)
              if (modifiedKeys?.length > 0) {
                // 触发改变的值
                this.triggerCodeChange(e.itemData)
              }
              if (this.flag) {
                this.$parent.$emit('handleReset', e.value)
              }
              this.flag = true
            },
            // 触发改变的值
            triggerCodeChange(selectData) {
              const args = {
                requestKey: dataKey, // 触发请求的key
                modifiedKeys: modifiedKeys || [], // 要被修改的key列表
                changeType: ComponentChangeType.code, // 修改类型
                data: selectData, // 发出的值
                modifiedRelation // 对应数据源中的 key 关系
              }
              this.$bus.$emit('forecastTypeColumnChange', args)
            }
          }
        })
      }
    }
    return template
  },
  // 公司
  companySelect: () => {
    return {
      template: Vue.component('companySelect', {
        template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <RemoteAutocomplete
                v-if="isShow"
                v-model="data.companyCode"
                url="/masterDataManagement/auth/company/auth-fuzzy"
                :placeholder="$t('请选择')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                records-position="data"
                @change="companyChange"
              />
              <span v-else>{{ companyName }}</span>
            </div>
          </div>`,
        data: function () {
          return {
            data: {},
            disabled: false,
            isShow: false,
            companyName: ''
          }
        },
        mounted() {
          if (rowDataTemp) {
            this.companyName = rowDataTemp[rowDataTemp.length - 1]['companyCode']
              ? rowDataTemp[rowDataTemp.length - 1]['companyCode'] +
                '-' +
                rowDataTemp[rowDataTemp.length - 1]['companyName']
              : ''
          }

          this.$bus.$on('forecastTypeColumnChange', (args) => {
            const { data, requestKey } = args
            if (requestKey === 'forecastType') {
              if (data.value === 2) {
                this.isShow = true
              } else {
                this.isShow = false
              }
            }
          })
          this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (args) => {
            const { data, requestKey } = args
            if (requestKey === 'deliveryCode') {
              this.companyName = data.companyCode + '-' + data.companyName
              rowDataTemp[rowDataTemp.length - 1]['companyCode'] = data.companyCode
              rowDataTemp[rowDataTemp.length - 1]['companyName'] = data.companyName
            }
          })
        },
        beforeDestroy() {
          this.$bus.$off('forecastTypeColumnChange')
          this.$bus.$off('reservationDeliverSupplierRowSearchDialogConfirm')
        },
        methods: {
          companyChange(e) {
            rowDataTemp[rowDataTemp.length - 1]['companyCode'] = e.value
            rowDataTemp[rowDataTemp.length - 1]['companyName'] = e.itemData?.orgName
          }
        }
      })
    }
  },
  // 交货方式
  deliveryStatusSelect: () => {
    return {
      template: Vue.component('deliveryStatusSelect', {
        template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                v-if="isShow"
                v-model="data.deliveryStatus"
                :data-source="deliveryStatusOptions"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="false"
                @change="deliveryStatusChange"
                :open-dispatch-change="false"
                :placeholder="$t('请选择')"
                :disabled="disabled"
              ></mt-select>
              <span v-else>{{ deliveryStatusName }}</span>
            </div>
          </div>`,
        data: function () {
          return {
            data: {},
            deliveryStatusOptions: [
              {
                text: this.$t('回收/退货'),
                value: 9
              },
              {
                text: this.$t('外仓送货'),
                value: 10
              }
            ],
            disabled: false,
            isShow: false,
            deliveryStatusName: ''
          }
        },
        mounted() {
          if (rowDataTemp) {
            let deliveryStatus = rowDataTemp[rowDataTemp.length - 1]['deliveryStatus']
            let deliveryStatusName =
              this.deliveryStatusOptions.find((v) => v.value === deliveryStatus)?.text || ''
            this.deliveryStatusName = deliveryStatusName
          }

          this.$bus.$on('forecastTypeColumnChange', (args) => {
            const { data, requestKey } = args
            if (requestKey === 'forecastType') {
              if (data.value === 2) {
                this.isShow = true
              } else {
                this.isShow = false
              }
            }
          })
          this.$bus.$on(`reservationDeliverSupplierRowSearchDialogConfirm`, (args) => {
            const { data, requestKey } = args
            if (requestKey === 'deliveryCode') {
              this.deliveryStatusName = data.deliveryStatus[0]?.text
              rowDataTemp[rowDataTemp.length - 1]['deliveryStatus'] = data.deliveryStatus
              rowDataTemp[rowDataTemp.length - 1]['deliveryStatusName'] =
                data.deliveryStatus[0]?.text
            }
          })
        },
        beforeDestroy() {
          this.$bus.$off('forecastTypeColumnChange')
          this.$bus.$off('reservationDeliverSupplierRowSearchDialogConfirm')
        },
        methods: {
          deliveryStatusChange(e) {
            rowDataTemp[rowDataTemp.length - 1]['deliveryStatus'] = [e.itemData]
            rowDataTemp[rowDataTemp.length - 1]['deliveryStatusName'] = [e.itemData?.text]
          }
        }
      })
    }
  },
  // 分厂
  subSiteSelect: () => {
    return {
      template: Vue.component('subSiteSelect', {
        template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                v-if="isShow"
                v-model="data.subSiteCode"
                :data-source="subSiteOptions"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :show-clear-button="true"
                :allow-filtering="false"
                @change="subSiteChange"
                :open-dispatch-change="false"
                :placeholder="$t('请选择')"
                :disabled="disabled"
              ></mt-select>
            </div>
          </div>`,
        data: function () {
          return {
            data: {},
            subSiteOptions: [],
            disabled: false,
            isShow: false
          }
        },
        mounted() {
          this.$bus.$on('forecastTypeColumnChange', (args) => {
            const { data, requestKey } = args
            if (requestKey === 'forecastType') {
              if (data.value === 2) {
                this.isShow = true
              } else {
                this.isShow = false
              }
            }
          })
          this.getSubSiteOptions()
        },
        beforeDestroy() {
          this.$bus.$off('forecastTypeColumnChange')
        },
        methods: {
          getSubSiteOptions() {
            this.$API.masterData
              .getDictCodeSupApi({ dictCode: 'KT_SUB_SITE_CODE_GATE' })
              .then((res) => {
                if (res.code === 200) {
                  this.subSiteOptions = res.data
                }
              })
          },
          subSiteChange(e) {
            rowDataTemp[rowDataTemp.length - 1]['subSiteCode'] = e.value
            rowDataTemp[rowDataTemp.length - 1]['subSiteName'] = e.itemData?.itemName
            let args = {
              requestKey: 'subSiteCode',
              data: e.itemData,
              options: e.itemData?.children || []
            }
            this.$bus.$emit('subSiteChange', args)
          }
        }
      })
    }
  },
  // 门岗号
  gateSelect: () => {
    return {
      template: Vue.component('gateSelect', {
        template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                v-if="isShow"
                v-model="data.gateNumber"
                :data-source="gateOptions"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :show-clear-button="true"
                :allow-filtering="false"
                @change="gateChange"
                :open-dispatch-change="false"
                :placeholder="$t('请选择')"
                :disabled="disabled"
              ></mt-select>
            </div>
          </div>`,
        data: function () {
          return {
            data: {},
            gateOptions: [],
            disabled: false,
            isShow: false
          }
        },
        mounted() {
          this.$bus.$on('forecastTypeColumnChange', (args) => {
            const { data, requestKey } = args
            if (requestKey === 'forecastType') {
              if (data.value === 2) {
                this.isShow = true
              } else {
                this.isShow = false
              }
            }
          })
          this.$bus.$on('subSiteChange', (args) => {
            const { requestKey, options } = args
            if (requestKey === 'subSiteCode') {
              this.gateOptions = options
            }
          })
        },
        beforeDestroy() {
          this.$bus.$off('forecastTypeColumnChange')
          this.$bus.$off('subSiteChange')
        },
        methods: {
          gateChange(e) {
            rowDataTemp[rowDataTemp.length - 1]['gateNumber'] = e.value
            rowDataTemp[rowDataTemp.length - 1]['gateNumberName'] = e.itemData?.itemName
          }
        }
      })
    }
  }
}
