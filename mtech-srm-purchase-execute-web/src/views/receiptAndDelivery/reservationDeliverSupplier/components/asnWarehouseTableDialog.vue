<template>
  <!-- 预约送货-供方-勾选关联ASN -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-template-page ref="templateRef" :template-config="componentConfig" :hidden-tabs="true" />
  </mt-dialog>
</template>

<script>
import { asnTableColumnData } from '../config/index.js'
import { AsnWarehouseTableColumnData } from '../config/constant'
import { BASE_TENANT } from '@/utils/constant'

export default {
  data() {
    return {
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            allowPaging: true, // 分页
            pageSettings: {
              pageSize: 20, // 每页显示条数
              pageSizes: [20, 50, 100, 200, 500, 1000] // 分页条数选项，最大1000
            },
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            columnData: asnTableColumnData({
              data: AsnWarehouseTableColumnData
            }),
            asyncConfig: {
              url: `${BASE_TENANT}/vmi-receive-order/pageForForecast`
            },
            dataSource: [],
            frozenColumns: 1
          }
        }
      ]
    }
  },

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.refreshColumns()
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      let selectedRowData = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.getMtechGridRecords()

      selectedRowData.forEach((item) => {
        item.deliveryCode = item.vmiOrderCode
      })
      this.$emit('confirm', { data: selectedRowData })
      this.handleClose()
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
