<template>
  <!-- 预约送货-采方 -->
  <div class="full-height pt20">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="false"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { formatTableColumnData, verifyStatus } from './config/index'
import { Toolbar, pvToolbar, ColumnData, Status } from './config/constant'
import { BASE_TENANT } from '@/utils/constant'
import { strByCharacterToArray, download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0 // 调用的api正在等待数
    }
  },
  computed: {
    toolbar() {
      // 根据事业部bu判断是使用Toolbar还是pvToolbar
      const currentBu = localStorage.getItem('currentBu') || 'TV'
      // 光伏事业部(GF)使用pvToolbar（只有导出）
      // 其他事业部使用Toolbar（包含通过、退回、同步、导出、复制送货单号、取消）
      return currentBu === 'GF' ? pvToolbar : Toolbar
    },
    templateConfig() {
      return [
        {
          toolbar: this.toolbar,
          buttonQuantity: 6,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.receiptAndDelivery.reservationDeliver.list,
          grid: {
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            allowPaging: true, // 分页
            // lineSelection: 0, // 选项列
            lineIndex: 0, // 序号列
            frozenColumns: 1, // 冻结第一列
            columnData: formatTableColumnData({
              data: ColumnData
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/buyerForecastDelivery/query`, // 采方预约送货-预约送货单列表
              serializeList: (list) => {
                list.forEach((item) => {
                  item.deliveryCode = strByCharacterToArray({
                    str: item.deliveryCode || '',
                    character: ','
                  })
                })
                return list
              }
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      // const selectRows = gridRef.getMtechGridRecords()
      let selectRows = []
      gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectRows.push(item)
        }
      })
      if (toolbar.id === 'export') {
        this.handleExport()
        return
      }
      const commonToolbar = [
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal',
        'synchronization'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIdList = []
      selectRows.forEach((item) => selectedIdList.push(item.id))

      if (toolbar.id === 'cancel') {
        // 已离园、已取消、已关闭的预约单不可取消
        const invalidRows = selectRows.filter((item) => {
          // 在途状态：已离园(12)、已取消(4)、已关闭(5)
          return item.onWayStatus === 12 || item.onWayStatus === 4 || item.onWayStatus === 5
        })

        if (invalidRows.length > 0) {
          const invalidMessages = []
          invalidRows.forEach((item) => {
            if (item.onWayStatus === 12) {
              invalidMessages.push(
                `${item.forecastCode || ''}: ${this.$t('已离园的预约单不可取消')}`
              )
            } else if (item.onWayStatus === 4) {
              invalidMessages.push(
                `${item.forecastCode || ''}: ${this.$t('已取消的预约单不可取消')}`
              )
            } else if (item.onWayStatus === 5) {
              invalidMessages.push(
                `${item.forecastCode || ''}: ${this.$t('已关闭的预约单不可取消')}`
              )
            }
          })

          this.$toast({
            content: invalidMessages.join('\n'),
            type: 'warning'
          })
          return
        }

        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消选中的数据？')
          },
          success: () => {
            this.handleCancel(selectRows)
          }
        })
      }

      if (toolbar.id === 'copy') {
        this.handleCopy(selectRows)
      }

      if (toolbar.id === 'BookPass') {
        // 通过 数据校验 状态 == 待审批
        const { valid, status } = verifyStatus(selectRows)
        if (!valid || status != Status.inProgress) {
          this.$toast({
            content: this.$t('非待审批的数据不可通过'),
            type: 'warning'
          })
        } else {
          this.handleBookPass({ selectedIdList })
        }
      } else if (toolbar.id === 'BookReturn') {
        // 退回 数据校验 状态 == 待审批
        const { valid, status } = verifyStatus(selectRows)
        if (!valid || status != Status.inProgress) {
          this.$toast({
            content: this.$t('非待审批的数据不可退回'),
            type: 'warning'
          })
        } else {
          this.handleBookReturn({ selectedIdList })
        }
      } else if (toolbar.id === 'synchronization') {
        this.hSynchronization(selectedIdList)
      }
    },
    handleCancel(selectRows) {
      if (!selectRows || selectRows.length === 0) {
        return
      }

      this.cancelBuyerForecastDelivery({
        idList: selectRows.map((item) => item.id)
      })
    },
    cancelBuyerForecastDelivery({ idList }) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .cancelBuyerForecastDeliveryApi(idList)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('取消成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleCopy(selectRows) {
      const deliveryCodeList = selectRows.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
      this.$router.push({
        name: `deliver-list`,
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    handleExport() {
      const asyncParams = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.asyncParams || {}
      let obj = JSON.parse(
        sessionStorage.getItem(this.$tableUUID.receiptAndDelivery.reservationDeliver.list)
      )?.visibleCols
      let includeColumnFiledNames = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            includeColumnFiledNames.push(item.field)
          }
        })
      } else {
        ColumnData.forEach((item) => {
          if (item.code) {
            includeColumnFiledNames.push(item.code)
          }
        })
      }
      let params = { ...asyncParams, sortedColumnStr: includeColumnFiledNames.join(',') }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.exportBuyerForecastDeliveryApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 同步
    hSynchronization(arr) {
      if (arr.length > 1) {
        this.$toast({ content: this.$t('只允许对一条数据进行操作'), type: 'warning' })
        return
      }
      const id = {
        id: arr.toString()
      }
      this.$API.receiptAndDelivery.purforecastSync(id).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args
      if (tool.id === 'BookPass') {
        // 通过
        this.handleBookPass({ selectedIdList: [data.id] })
      } else if (tool.id === 'BookReturn') {
        // 退回
        this.handleBookReturn({ selectedIdList: [data.id] })
      }
    },
    // 通过
    handleBookPass(args) {
      const { selectedIdList } = args
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认通过选中的数据？')
        },
        success: () => {
          this.postBuyerForecastDeliveryStatus({
            idList: selectedIdList,
            status: Status.success
          })
        }
      })
    },
    // 退回
    handleBookReturn(args) {
      const { selectedIdList } = args
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认退回选中的数据？')
        },
        success: () => {
          this.postBuyerForecastDeliveryStatus({
            idList: selectedIdList,
            status: Status.reject
          })
        }
      })
    },
    // 预约送货-采方-预约送货审批
    postBuyerForecastDeliveryStatus(args) {
      const { idList, status } = args
      const params = {
        body: idList,
        status
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postBuyerForecastDeliveryStatus(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
