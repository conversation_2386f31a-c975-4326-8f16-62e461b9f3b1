<!-- 供方-扫描装车记录 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <ListView ref="listViewRef" v-show="tabIndex == 0" />
      <DetailView ref="detailViewRef" v-show="tabIndex == 1" />
    </div>
  </div>
</template>

<script>
import ListView from './pages/ListView.vue'
import DetailView from './pages/DetailView.vue'

export default {
  components: { ListView, DetailView },
  data() {
    return {
      tabList: [{ title: this.$t('列表视图') }, { title: this.$t('明细视图') }],
      selectedItem: 0,
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
