/*
 * @Author: wb_qianxiaoxia <EMAIL>
 * @Date: 2025-06-17 21:55:50
 * @LastEditors: wb_qianxiaoxia <EMAIL>
 * @LastEditTime: 2025-07-07 14:47:06
 * @LastEditors: ex_jinfeng.shi <EMAIL>
 * @LastEditTime: 2025-06-23 14:36:54
 * @FilePath: /mtech-srm-purchase-execute-web/src/apis/modules/sendSampleCertification.js
 * @Description:
 */
import { API } from '@mtech-common/http'

export const NAME = 'sendSampleCertification'

// 采方-送样申请单-分页查询
export const pageSendSampleApplicationFormApi = (data = {}) =>
  API.post(`/contract/tenant/plmSampleApply/pageQuery`, data)
// 采方-认证申请单-分页查询
export const pageQueryCertificationApplicationFormApi = (data = {}) =>
  API.post(`/sourcing/tenant/authApply/pageQuery`, data)

// 采方-审批认证单，通过或驳回
export const approveCertificationApplicationFormApi = (data = {}) =>
  API.post(`/sourcing/tenant/authApply/approveData`, data)

// 供方-送样申请单-分页查询
export const pageSendSampleApplicationFormSupApi = (data = {}) =>
  API.post(`/contract/tenant/plmSampleApply/pageQueryForSupplier`, data)

// 供方-送样申请单-提交
export const submitSendSampleApplicationFormSupApi = (data = {}) =>
  API.post(`/contract/tenant/sendSampleApplicationForm/supplier/submit`, data)

// 采方-供方  详情
export const getAuthApplyInfo = (id = '') =>
  API.get(`/contract/tenant/plmSampleApply/getAuthApplyInfo/${id}`)

// 采方-供方  保存
export const plmSampleApplyUpdate = (data = {}) =>
  API.post(`/contract/tenant/plmSampleApply/updateData`, data)

// 供方  提交
export const plmSampleApplySubmitData = (data = {}) =>
  API.post(`/contract/tenant/plmSampleApply/submitData`, data)

// 采方驳回
export const plmSampleApplyReject = (data = {}) =>
  API.post(`/contract/tenant/plmSampleApply/rejectData`, data)

export const plmSampleApplyConfirmData = (data = {}) =>
  API.post(`/contract/tenant/plmSampleApply/confirmData`, data)

// 下载
export const getPlmFileUrl = (id = '') =>
  API.get(`/contract/tenant/plmSampleApply/getPlmFileUrl/${id}`)
// 供方-认证申请单-保存
export const saveCertificationApplicationFormSupApi = (data = {}) =>
  API.post(`/sourcing/tenant/authApply/updateData`, data)

// 供方-认证申请单-批量提交
export const submitCertificationApplicationFormSupApi = (data = {}) =>
  API.post(`/sourcing/tenant/authApply/submitData`, data)

// 供方-认证申请单-详情查询
export const getCertificationApplicationFormSupDetailApi = (id) =>
  API.get(`/sourcing/tenant/authApply/getAuthApplyInfo/${id}`, { id })

// 供方-认证清单-分页查询
export const pageQueryCertificationListForSupplierApi = (data = {}) =>
  API.post(`/sourcing/tenant/authApply/pageQueryForSupplier`, data)

// 获取二级物料选项
export const getSecondMaterialApi = (data = {}) =>
  API.post(`/sourcing/tenant/authApply/getSecondMaterial`, data)
