<!-- 交货计划 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('序列号')" prop="serialNumber">
          <mt-input
            v-model="searchFormModel.serialNumber"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('业务类型')" prop="businessTypeList">
          <mt-multi-select
            v-model="searchFormModel.businessTypeList"
            :data-source="businessTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="itemCodeStr">
          <mt-input
            v-model="searchFormModel.itemCodeStr"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否SRM签收')" prop="srmSignFlag">
          <mt-select
            v-model="searchFormModel.srmSignFlag"
            :data-source="srmSignFlagOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="demandDate" :label="$t('需求日期')">
          <mt-date-range-picker
            v-model="searchFormModel.demandDate"
            @change="(e) => dateTimeChange(e, 'demandDate')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="a3529371-adbd-17c1-d2f9-9c483e18b66d"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #shipmentQuantityEdit="{ row }">
        <vxe-input
          type="number"
          v-model="row.shipmentQuantity"
          :placeholder="$t('请输入')"
          clearable
          transfer
          :min="0"
          :max="row.pendingShipmentQuantity"
        />
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import {
  deliveryScheduleColumnData,
  businessTypeOptions,
  srmSignFlagOptions
} from '../config/index'
export default {
  components: { CollapseSearch, ScTable, RemoteAutocomplete },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [{ code: 'create', name: this.$t('创建签收单'), status: 'info', loading: false }],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: deliveryScheduleColumnData,
      loading: false,
      tableData: [],

      businessTypeOptions,
      srmSignFlagOptions,

      editRules: {
        shipmentQuantity: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    beforeEditMethod({ row }) {
      if (!row.pendingShipmentQuantity) {
        return false
      }
      return true
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.receivingManagement
        .pageDeliveryScheduleApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records.map((item) => {
          return {
            ...item,
            shipmentQuantity: item.pendingShipmentQuantity
          }
        })
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['create']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'create':
          this.handleCreate(selectedRecords)
          break
        default:
          break
      }
    },
    handleCreate(selectedRecords) {
      let dtoList = selectedRecords.map((item) => {
        return {
          id: item?.id,
          baseUnitCode: item?.unitCode,
          baseUnitName: item?.unitName,
          categoryCode: item?.categoryCode,
          categoryName: item?.categoryName,
          deliveryQty: item?.shipmentQuantity,
          itemCode: item?.itemCode,
          itemName: item?.itemName,
          largeCategoryName: item?.largeCategoryName,
          mediumCategoryName: item?.mediumCategoryName,
          smallCategoryName: item?.smallCategoryName,
          requiredDeliveryDate: item?.demandDate,
          specificationModel: item?.specificationModel,
          serialNumber: item?.serialNumber
        }
      })
      this.$API.receivingManagement.preCreatePlanSupplyPlanApi(dtoList).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            sessionStorage.setItem('supplyPlanSelectedRecords', JSON.stringify(res.data))
            sessionStorage.setItem('itemTabData', JSON.stringify(res.data))
            this.$router.push({
              path: '/purchase-pv/receiving-management/supply-plan-detail',
              query: {
                type: 'create',
                source: 'plan',
                timeStamp: new Date().getTime()
              }
            })
          }
        }
      })
    }
  }
}
</script>
